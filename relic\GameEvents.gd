extends Node

## 当弹球击中敌人时触发
## @ball: 击中敌人的弹球实例
## @enemy: 被击中的敌人实例
## @collision_info: 包含碰撞信息的字典，例如碰撞点、法线等
signal ball_hit_enemy(ball: Node, enemy: Node, collision_info: Dictionary)

## 当敌人受到伤害时触发
## @enemy: 受到伤害的敌人实例
## @damage_info: 包含伤害信息的字典，例如伤害值、伤害类型、是否暴击等
signal enemy_received_damage(enemy: Node, damage_info: Dictionary)

## 当玩家受到伤害时触发
## @player: 受到伤害的玩家实例
## @damage_info: 包含伤害信息的字典
signal player_received_damage(player: Node, damage_info: Dictionary)

## 当敌人攻击时触发
## @enemy: 发动攻击的敌人实例
signal enemy_attacked(enemy: Node)

## 当敌人死亡时触发
## @enemy: 死亡的敌人实例
## @killer: 造成致命一击的实体，可能是弹球或玩家效果
signal enemy_died(enemy: Node, killer: Node)

## 当一个新的回合/波次开始时触发
signal round_started 