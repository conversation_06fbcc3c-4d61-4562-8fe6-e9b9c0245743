class_name PoisonEffectHandler
extends BuffHandlerBase

## 将灼烧需要的资源暴露出来，方便在编辑器里配置
@export var posion_material: ShaderMaterial
@export var posion_to_die_material: ShaderMaterial
@export var posion_particle: PackedScene
@export var damage_color: Color = Color.WHITE

# 毒素传播相关参数
@export var splash_radius: float = 1000.0 # 传播范围
@export var splash_probability: float = 0.9 # 传播概率(0-1)
@export var splash_on_dot: bool = true # 是否在DOT触发时传播
@export var splash_on_death: bool = true # 是否在死亡时传播

# 实现具体的添加逻辑
func on_buff_added(target: Node, buff: AttributeBuff):
	if not is_instance_valid(target) or not is_instance_valid(buff):
		return
	
	target.sprite.add_material(handled_buff_name, posion_material)
	apply_poison_particle(target)
	
	# 连接DOT信号，我们只绑定target，因为buff会由信号自己传来
	if buff is AttributeBuffDOT and not buff.is_connected("dot_triggered", Callable(self, "on_dot_triggered")):
		buff.dot_triggered.connect(Callable(self, "on_dot_triggered").bind(target))


# 实现具体的移除逻辑
func on_buff_removed(target: Node, buff: AttributeBuff):
	if not is_instance_valid(target) or not is_instance_valid(buff):
		return
	target.sprite.erase_material(handled_buff_name)
	ParticlesPoolManager.recycle_particle(target.get_node("PoisonParticle"))


func handle_enemy_death_effect(target: EnemyBase) -> bool:
	if not is_instance_valid(target) or not posion_to_die_material:
		return false
		
	var sprite = target.sprite
	sprite.add_material("posion_to_die", posion_to_die_material)
	sprite.set_instance_shader_param_by_name("posion_to_die", "progress", -1.0)
	
	var tween = target.create_tween()
	tween.tween_method(func(value: float): sprite.set_instance_shader_param_by_name("posion_to_die", "percentage", value), 1.0, 0.0, 2.0)
	tween.finished.connect(func(): target.queue_free())

	var particle = target.get_node("PoisonParticle") as GPUParticles2D
	if particle:
		target.create_tween().tween_property(particle, "modulate:a", 0.0, 2.0)
	
	# 死亡时尝试传播毒素
	if splash_on_death:
		random_splash(target, target.find_attribute("hp").find_buff("poison"))
	
	return true

# 实现具体的DOT触发逻辑
# 签名现在接收3个参数：2个来自信号emit，1个来自bind
func on_dot_triggered(attribute: Attribute, buff: AttributeBuff, target: Node):
	if not is_instance_valid(target) or not target.has_method("take_damage"):
		return

	target.take_damage(buff.stack_value, damage_color)
	
	# DOT触发时尝试传播毒素
	if splash_on_dot and randf() <= splash_probability:
		random_splash(target, buff)


func apply_poison_particle(enemy: EnemyBase):
	if not is_instance_valid(enemy) or enemy.has_node("PoisonParticle"):
		return
		
	# 实例化粒子
	var particle_instance = ParticlesPoolManager.get_particle(ParticlesPoolManager.ParticleType.POISON_PARTICLE, Vector2.ZERO, enemy)
	particle_instance.name = "PoisonParticle"
	
	# 获取敌人的 Sprite2D 节点
	var sprite = enemy.sprite
	if sprite:
		# 获取 Sprite 的矩形区域大小
		var sprite_size = sprite.get_rect().size
		# 考虑 Sprite 的缩放
		var scaled_sprite_size = sprite_size * sprite.scale * 0.5
		
		# 获取粒子的处理材质
		var material = particle_instance.process_material as ParticleProcessMaterial
		if material:
			# 设置发射形状为盒子
			material.emission_shape = ParticleProcessMaterial.EMISSION_SHAPE_BOX
			
			# 设置发射盒的大小为 Sprite 大小的一半
			material.emission_box_extents = Vector3(scaled_sprite_size.x / 2.0, scaled_sprite_size.y / 2.0, 0)


# 实现毒素随机传播到范围内的一个敌人
func random_splash(source_enemy: Node, buff: AttributeBuff):
	# 如果不是有效的buff或者敌人，直接返回
	if not is_instance_valid(source_enemy) or not is_instance_valid(buff):
		return
	
	# 获取场景中所有的敌人
	var all_enemies = source_enemy.get_tree().get_nodes_in_group("enemy")
	var valid_targets = []
	
	# 找出范围内的其他敌人
	for enemy in all_enemies:
		if enemy != source_enemy and enemy is EnemyBase and not enemy.is_dead:
			var distance = source_enemy.global_position.distance_to(enemy.global_position)
			if distance <= splash_radius:
				valid_targets.append(enemy)
	
	# 如果没有有效目标，直接返回
	if valid_targets.size() == 0:
		return
	
	# 随机选择一个敌人
	var target_enemy = valid_targets[randi() % valid_targets.size()]
	
	# 创建一个粒子效果连接源和目标（可选）
	_create_splash_effect(source_enemy, target_enemy)
	

# 创建一个从源到目标的粒子效果，视觉上表示传播（可选）
func _create_splash_effect(source_enemy: EnemyBase, target_enemy: EnemyBase):
	# 这个函数可以创建一个临时的粒子效果来显示传播路径
	if not is_instance_valid(source_enemy) or not is_instance_valid(target_enemy):
		return
	
	# 创建粒子实例
	var splash_particle = ParticlesPoolManager.get_particle(ParticlesPoolManager.ParticleType.POISON_SPLASH, source_enemy.global_position)
	splash_particle.move(source_enemy, target_enemy)
