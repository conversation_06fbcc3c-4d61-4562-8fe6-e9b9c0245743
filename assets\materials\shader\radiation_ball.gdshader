shader_type canvas_item;

// 动画速度控制
uniform float time_scale : hint_range(0.0, 10.0) = 1.0;
uniform float time_offset : hint_range(0.0, 10.0) = 0.0;

// 颜色控制
uniform vec4 core_color : source_color = vec4(1.0, 0.8, 0.3, 1.0);
uniform vec4 edge_color : source_color = vec4(1.0, 0.3, 0.1, 1.0);
instance uniform float overall_alpha : hint_range(0.0, 1.0) = 1.0;

// 大小和形状控制
uniform float size_scale : hint_range(0.1, 5.0) = 1.0;
uniform float edge_softness : hint_range(0.0, 2.0) = 1.0;
uniform float core_size : hint_range(0.0, 2.0) = 1.0;
uniform float edge_size : hint_range(0.0, 2.0) = 0.5;

// 噪声控制
uniform float noise_scale : hint_range(1.0, 100.0) = 16.0;
uniform float noise_strength : hint_range(0.0, 2.0) = 1.0;

float snoise(vec3 uv, float res)
{
    const vec3 s = vec3(1e0, 1e2, 1e3);
    uv *= res;

    vec3 uv0 = floor(mod(uv, res)) * s;
    vec3 uv1 = floor(mod(uv + vec3(1.0), res)) * s;

    vec3 f = fract(uv);
    f = f * f * (3.0 - 2.0 * f);

    vec4 v = vec4(uv0.x + uv0.y + uv0.z, uv1.x + uv0.y + uv0.z,
                  uv0.x + uv1.y + uv0.z, uv1.x + uv1.y + uv0.z);

    vec4 r = fract(sin(v * 1e-1) * 1e3);
    float r0 = mix(mix(r.x, r.y, f.x), mix(r.z, r.w, f.x), f.y);

    r = fract(sin((v + uv1.z - uv0.z) * 1e-1) * 1e3);
    float r1 = mix(mix(r.x, r.y, f.x), mix(r.z, r.w, f.x), f.y);

    return mix(r0, r1, f.z) * 2.0 - 1.0;
}

void fragment()
{
    // 应用大小缩放
    vec2 p = (UV - 0.5) * size_scale;
    p.x *= TEXTURE_PIXEL_SIZE.y / TEXTURE_PIXEL_SIZE.x;

    float base_shape = 3.0 - (3.0 * length(2.0 * p));

    vec3 coord = vec3(atan(p.x, p.y) / 6.2832 + 0.5, length(p) * 0.4, 0.5);

    float noise = 0.0;
    for (int i = 1; i <= 7; i++)
    {
        float power = pow(2.0, float(i));
        noise += (1.5 / power) * snoise(coord + vec3(0.0,
            -time_offset - TIME * time_scale * 0.05,
            time_offset + TIME * time_scale * 0.01
        ), power * floor(noise_scale));
    }

    // 应用噪声强度
    float color = base_shape + noise * noise_strength;

    // 计算核心和边缘
    float core_mask = smoothstep(0.0, core_size, color);
    float edge_mask = smoothstep(0.0, edge_size, color);

    // 混合核心和边缘颜色
    vec3 final_color = mix(edge_color.rgb, core_color.rgb, core_mask);

    // 计算alpha值，使用edge_softness控制边缘软度
    float alpha = smoothstep(0.0, edge_softness, edge_mask);

    COLOR = vec4(final_color, alpha * overall_alpha);
}
