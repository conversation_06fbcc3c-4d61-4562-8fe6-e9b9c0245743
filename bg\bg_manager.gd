extends Node
class_name BGManager

@export var environment_themes: Array[EnvironmentTheme]

func _ready() -> void:
	if environment_themes.is_empty():
		push_error("No environment themes configured in BGManager.")
		return

	environment_themes.shuffle()
	var selected_theme: EnvironmentTheme = environment_themes.pop_front()

	if not selected_theme:
		push_error("Selected theme is null.")
		return

	print("selected_theme: ", selected_theme.name)
	GameManager.current_theme = selected_theme

	if selected_theme.background_scene:
		var bg_instance = selected_theme.background_scene.instantiate()
		bg_instance.name = "Bg"
		add_child(bg_instance)
	else:
		push_warning("Selected theme is missing a background scene.")

func _process(delta: float) -> void:
	pass
