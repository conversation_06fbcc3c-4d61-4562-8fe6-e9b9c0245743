---
description: 
globs: 
alwaysApply: true
---
---
# EvolutionBall 游戏玩法与基础机制

## 游戏类型
- 基于 Godot 4.4.1 的弹球打砖块类2D手机小游戏。

## 一、核心玩法
- 玩家通过控制一个角色，移动并调整弹球的发射方向（支持鼠标或触控操作）。
- 弹球以队列形式存储，按一定间隔依次发射。
- 玩家可选择弹球的种类和数量。

## 二、玩家机制
- 玩家拥有血量，血量归零则游戏结束。
- 弹球具有攻击力，不同弹球拥有不同特效（如燃烧、冰冻等）。
- 弹球发射后计时，达到一定时间后会受到重力影响加速下落。
- 弹球下落碰到"deadline"后会被回收，重新加入队列等待下次发射。

## 四、升级与成长
- 升级时可选择弹球升级或获得新弹球。
- 击破敌人有概率掉落道具，可用于弹球融合或弹球进化。

## 五、难度与分数
- 随时间推移，敌人的血量和数量逐渐增加。
- 游戏结束时，根据击破敌人的数量计算分数，并支持好友分数排行。


