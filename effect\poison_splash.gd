class_name PoisonSplash
extends Node2D

@onready var sprite: Sprite2D = $Sprite2D
@onready var poison_ability: PoisonAbility = $PoisonAbility
@onready var trail: Line2D = $Trail
@onready var particles: GPUParticles2D = $GPUParticles2D
@export var move_duration: float = 1.0
@export var fade_duration: float = 0.5
@export var arc_height_factor: float = 0.3

signal finished

var active_tween: Tween

func play():
	# Cancel any previous animation on the same object
	if active_tween and active_tween.is_valid():
		active_tween.kill()

	sprite.set_instance_shader_parameter("overall_alpha", 1.0)
	trail.set_instance_shader_parameter("overall_alpha", 1.0)
	particles.modulate.a = 1.0
	particles.restart()
	trail.clear_points()


func move(source: Node, target: Node):
	var source_pos = source.global_position
	if not is_instance_valid(source) or not is_instance_valid(target):
		finished.emit()
		return

	self.global_position = source_pos

	# --- Calculate a control point to create a curved path ---
	var initial_target_pos = target.global_position
	var direction = initial_target_pos - source_pos
	var perpendicular = direction.orthogonal().normalized()
	# Add randomness to the arc height and direction
	var arc_height         = direction.length() * (arc_height_factor + randf_range(-0.1, 0.1))
	var offset_side: float = 1.0 if randf() > 0.5 else -1.0
	var control_point      = source_pos + direction * 0.5 + perpendicular * arc_height * offset_side
	var target_id = target.get_instance_id()

	# Cancel any existing animation before creating a new one
	if active_tween and active_tween.is_valid():
		active_tween.kill()

	# Create a single tween for the whole sequence
	active_tween = create_tween()
	active_tween.set_parallel(false) # Ensure sequential execution

	# 1. Move to target by updating position each frame along a curve
	active_tween.tween_method(
		func(weight): _update_position_curved(weight, source_pos, control_point, initial_target_pos, target_id),
		0.0, 1.0, move_duration
	)

	# 2. Apply buff via callback
	active_tween.tween_callback(func():
		var target_node = instance_from_id(target_id)
		if is_instance_valid(target_node) and target_node.has_method("add_buff"):
			poison_ability.on_primary_hit(null, target_node)
	)

	# 3. Fade out particles
	active_tween.tween_method(func(weight): fade_out(1.0 - weight), 0.0, 1.0, fade_duration)

	# 4. Emit finished signal when the entire sequence is done
	active_tween.tween_callback(finished.emit)


func fade_out(alpha: float):
	particles.modulate.a = alpha
	sprite.set_instance_shader_parameter("overall_alpha", alpha)
	trail.set_instance_shader_parameter("overall_alpha", alpha)


func _update_position_curved(weight: float, p0: Vector2, p1: Vector2, initial_p2: Vector2, target_id: int):
	var target_node = instance_from_id(target_id)

	var p2: Vector2 = initial_p2
	if is_instance_valid(target_node):
		p2 = target_node.global_position

	# Quadratic Bézier interpolation
	global_position = p0.lerp(p1, weight).lerp(p1.lerp(p2, weight), weight)
