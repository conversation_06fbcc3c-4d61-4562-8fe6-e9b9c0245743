class_name LevelUIData
extends Resource

## 等级UI数据类
##
## 用于在升级面板中显示等级信息的数据结构
## 包含当前等级、下一等级、经验值等信息

## 当前等级
var current_level: int = 1

## 下一等级
var next_level: int = 2

## 当前经验值
var current_exp: float = 0.0

## 升级所需经验值
var required_exp: float = 100.0

## 经验值进度（0.0-1.0）
var exp_progress: float = 0.0

## 构造函数
## @param p_current_level: 当前等级
## @param p_next_level: 下一等级
## @param p_current_exp: 当前经验值
## @param p_required_exp: 升级所需经验值
func _init(
	p_current_level: int = 1,
	p_next_level: int = 2,
	p_current_exp: float = 0.0,
	p_required_exp: float = 100.0
):
	current_level = p_current_level
	next_level = p_next_level
	current_exp = p_current_exp
	required_exp = p_required_exp
	exp_progress = current_exp / required_exp if required_exp > 0 else 0.0

## 获取格式化的等级信息字符串
## @return: 格式化的等级信息
func get_formatted_info() -> String:
	var info_parts: Array[String] = []

	info_parts.append("当前等级: " + str(current_level))
	info_parts.append("下一等级: " + str(next_level))
	info_parts.append("经验值: %.1f / %.1f" % [current_exp, required_exp])
	info_parts.append("进度: %.1f%%" % (exp_progress * 100))

	return "\n".join(info_parts)

## 获取等级显示字符串
## @return: 等级显示字符串（如"等级 5 → 6"）
func get_level_display() -> String:
	return "等级 %d → %d" % [current_level, next_level]

## 获取经验值显示字符串
## @return: 经验值显示字符串
func get_exp_display() -> String:
	return "%.0f / %.0f" % [current_exp, required_exp]

## 获取进度百分比字符串
## @return: 进度百分比字符串
func get_progress_display() -> String:
	return "%.1f%%" % (exp_progress * 100)

## 检查是否可以升级
## @return: 是否可以升级
func can_level_up() -> bool:
	return current_exp >= required_exp

## 获取距离升级还需要的经验值
## @return: 还需要的经验值
func get_exp_needed() -> float:
	return max(0.0, required_exp - current_exp)

## 检查数据是否有效
## @return: 数据是否有效
func is_valid() -> bool:
	return current_level > 0 and next_level > current_level and required_exp > 0

## 创建数据的副本
## @return: 新的LevelUIData实例
func create_copy() -> LevelUIData:
	return LevelUIData.new(
		current_level,
		next_level,
		current_exp,
		required_exp
	)
