[gd_scene load_steps=40 format=3 uid="uid://bnjnk8a884ewg"]

[ext_resource type="PhysicsMaterial" uid="uid://d0d4n7iiiu1o8" path="res://assets/materials/physics/ball.tres" id="1_ukdki"]
[ext_resource type="Script" uid="uid://drjqkf3mt3gs1" path="res://ball/ball_base.gd" id="2_o70ro"]
[ext_resource type="Material" uid="uid://bnshtdvmq5lpf" path="res://ball/effect/ball_sprite_material/fire_ball.tres" id="3_2sac1"]
[ext_resource type="Script" uid="uid://boltc4gkngyox" path="res://ui/res/ball_ui_resource.gd" id="3_6gv2j"]
[ext_resource type="Script" uid="uid://cf2dpucierrop" path="res://ball/ability/fire_ability.gd" id="3_d6hjy"]
[ext_resource type="Script" uid="uid://3lqxnj5nr7f1" path="res://addons/attribute_manager/AttributeComponent.gd" id="3_jfk08"]
[ext_resource type="Texture2D" uid="uid://donaevmmjeptc" path="res://assets/imgs/balls/普通球.png" id="4_d6hjy"]
[ext_resource type="Script" uid="uid://coux00joxi7h1" path="res://addons/attribute_manager/Attribute.gd" id="4_l38oo"]
[ext_resource type="Script" uid="uid://c4hm0ggsbq3bl" path="res://addons/trail/trail.gd" id="4_o70ro"]
[ext_resource type="Shader" uid="uid://bionbfobjqg6w" path="res://assets/materials/shader/radiation_ball.gdshader" id="5_d6hjy"]
[ext_resource type="Shader" uid="uid://b7lnuflpbunq7" path="res://assets/materials/shader/fluid.gdshader" id="5_o70ro"]
[ext_resource type="Script" uid="uid://5ww2mn76prh4" path="res://attribute/leveled_attribute.gd" id="5_wpedu"]
[ext_resource type="Script" uid="uid://2dxckbgqoga5" path="res://addons/attribute_manager/AttributeSet.gd" id="6_j3avg"]
[ext_resource type="Texture2D" uid="uid://iuhktnnpcg8n" path="res://assets/imgs/effects/T_VFX_Glow_1.PNG" id="7_o70ro"]
[ext_resource type="Resource" uid="uid://8giqogidxinh" path="res://ball/effect/burn_effect/burn_config.tres" id="13_l38oo"]
[ext_resource type="Script" uid="uid://b14ywpmhnqjgo" path="res://ui/res/ability_ui_resource.gd" id="15_r43o2"]

[sub_resource type="Resource" id="Resource_r43o2"]
script = ExtResource("3_6gv2j")
ball_name = "火焰"
description = "命中伤害 [color=#ff9000][min_damage]-[max_damage][/color]"
icon_type = 2
icon_text = ""
icon_shader_material = ExtResource("3_2sac1")
rarity = "普通"
metadata/_custom_type_script = "uid://boltc4gkngyox"

[sub_resource type="Resource" id="Resource_kl45d"]
script = ExtResource("4_l38oo")
base_value = 1.0
can_cache = true
metadata/_custom_type_script = "uid://coux00joxi7h1"

[sub_resource type="Resource" id="Resource_pu0ju"]
script = ExtResource("5_wpedu")
growth_per_level = 8.0
base_value = 16.0
can_cache = true
metadata/_custom_type_script = "uid://5ww2mn76prh4"

[sub_resource type="Resource" id="Resource_bkvia"]
script = ExtResource("5_wpedu")
growth_per_level = 3.0
base_value = 7.0
can_cache = true
metadata/_custom_type_script = "uid://5ww2mn76prh4"

[sub_resource type="Resource" id="Resource_l38oo"]
script = ExtResource("4_l38oo")
base_value = 500.0
can_cache = true
metadata/_custom_type_script = "uid://coux00joxi7h1"

[sub_resource type="Resource" id="Resource_obbgq"]
resource_local_to_scene = true
script = ExtResource("6_j3avg")
attributes = Dictionary[StringName, ExtResource("4_l38oo")]({
&"level": SubResource("Resource_kl45d"),
&"max_damage": SubResource("Resource_pu0ju"),
&"min_damage": SubResource("Resource_bkvia"),
&"speed": SubResource("Resource_l38oo")
})
metadata/_custom_type_script = "uid://2dxckbgqoga5"

[sub_resource type="CircleShape2D" id="CircleShape2D_yi17e"]
radius = 15.0

[sub_resource type="ShaderMaterial" id="ShaderMaterial_pwu0t"]
resource_local_to_scene = true
shader = ExtResource("5_d6hjy")
shader_parameter/time_scale = 1.0
shader_parameter/time_offset = 0.0
shader_parameter/core_color = Color(1.3, 0.8, 0.3, 1)
shader_parameter/edge_color = Color(1.1, 0.3, 0.1, 1)
shader_parameter/size_scale = 1.63
shader_parameter/edge_softness = 1.0
shader_parameter/core_size = 1.181
shader_parameter/edge_size = 0.0
shader_parameter/noise_scale = 40.0
shader_parameter/noise_strength = 0.676

[sub_resource type="FastNoiseLite" id="FastNoiseLite_o70ro"]
resource_local_to_scene = true
noise_type = 4
seed = 435
frequency = 0.001
fractal_type = 3
fractal_gain = 1.235

[sub_resource type="NoiseTexture2D" id="NoiseTexture2D_mspxm"]
resource_local_to_scene = true
seamless = true
seamless_blend_skirt = 1.0
noise = SubResource("FastNoiseLite_o70ro")

[sub_resource type="FastNoiseLite" id="FastNoiseLite_mspxm"]
frequency = 0.0003

[sub_resource type="NoiseTexture2D" id="NoiseTexture2D_cyrwm"]
resource_local_to_scene = true
seamless = true
seamless_blend_skirt = 1.0
noise = SubResource("FastNoiseLite_mspxm")

[sub_resource type="Gradient" id="Gradient_d6hjy"]
offsets = PackedFloat32Array(0.07, 0.168333, 0.283333, 0.395, 0.578333, 1)
colors = PackedColorArray(0.207635, 0.0105196, 0.0125952, 0.90852, 0.944686, 0.191471, 0.192284, 0.90852, 0.960784, 0.141176, 0.2, 0.0235294, 0.21, 0.0105, 0.0138249, 0.701961, 0.995848, 0.0149481, 0.214025, 0.896609, 1, 0.6, 0, 1)

[sub_resource type="GradientTexture1D" id="GradientTexture1D_cyrwm"]
gradient = SubResource("Gradient_d6hjy")
use_hdr = true

[sub_resource type="ShaderMaterial" id="ShaderMaterial_hvdpe"]
resource_local_to_scene = true
shader = ExtResource("5_o70ro")
shader_parameter/noise1 = SubResource("NoiseTexture2D_mspxm")
shader_parameter/noise2 = SubResource("NoiseTexture2D_cyrwm")
shader_parameter/scroll1 = Vector2(0.15, 0.25)
shader_parameter/scroll2 = Vector2(-0.15, -0.25)
shader_parameter/tex2_scale = 1.0
shader_parameter/overlap_color_gradient = SubResource("GradientTexture1D_cyrwm")
shader_parameter/overlap_factor = 1.5
shader_parameter/color_factor = 1.4
shader_parameter/blur = 1.0
shader_parameter/delay_v = 0.4
shader_parameter/delay_type = 0
shader_parameter/embed = true
shader_parameter/edge_threshold = 0.1
shader_parameter/edge_softness = 0.04
shader_parameter/edge_noise_scale = 1.6
shader_parameter/edge_noise_influence = 1.0
shader_parameter/edge_noise_scroll = Vector2(0.05, 0.03)
shader_parameter/edge_direction_mode = 0
shader_parameter/use_multiple_edges = true
shader_parameter/edge_left = true
shader_parameter/edge_right = false
shader_parameter/edge_top = true
shader_parameter/edge_bottom = true
shader_parameter/edge_radial = false
shader_parameter/edge_animation_speed = 15.0

[sub_resource type="Curve" id="Curve_yi17e"]
_data = [Vector2(0, 0), 0.0, 0.0, 0, 0, Vector2(1, 1), 0.0, 0.0, 0, 0]
point_count = 2

[sub_resource type="CanvasItemMaterial" id="CanvasItemMaterial_mspxm"]
blend_mode = 1

[sub_resource type="Curve" id="Curve_mspxm"]
_data = [Vector2(0, 0), 0.0, 0.0, 0, 0, Vector2(0.415865, 0), 0.0, 0.0, 0, 0, Vector2(0.451923, 1), 0.0, 0.0, 0, 0, Vector2(1, 0), 0.0, 0.0, 0, 0]
point_count = 4

[sub_resource type="CurveTexture" id="CurveTexture_cyrwm"]
curve = SubResource("Curve_mspxm")

[sub_resource type="Curve" id="Curve_o70ro"]
_data = [Vector2(0, 0.331701), 0.0, 0.0, 0, 0, Vector2(1, 0.331701), 0.0, 0.0, 0, 0]
point_count = 2

[sub_resource type="CurveTexture" id="CurveTexture_hvdpe"]
curve = SubResource("Curve_o70ro")

[sub_resource type="ParticleProcessMaterial" id="ParticleProcessMaterial_lbb5j"]
lifetime_randomness = 0.45
particle_flag_disable_z = true
emission_shape = 1
emission_sphere_radius = 20.0
gravity = Vector3(0, 0, 0)
scale_min = 0.02
scale_max = 0.03
scale_curve = SubResource("CurveTexture_hvdpe")
color = Color(1.3, 0.9, 0, 1)
alpha_curve = SubResource("CurveTexture_cyrwm")

[sub_resource type="Resource" id="Resource_2sac1"]
script = ExtResource("15_r43o2")
description = "对击中敌人施加[color=#ff9000]1[/color]层燃烧状态。燃烧效果持续[color=#ff9000][duration][/color]秒，每层燃烧每[color=#ff9000][period][/color]秒造成[color=#ff9000][value][/color]伤害，最多叠加[color=#ff9000][max_stacks][/color]层"
metadata/_custom_type_script = "uid://b14ywpmhnqjgo"

[node name="FireBall" type="RigidBody2D"]
collision_layer = 2
collision_mask = 13
physics_material_override = ExtResource("1_ukdki")
gravity_scale = 0.0
can_sleep = false
lock_rotation = true
linear_damp_mode = 1
angular_damp_mode = 1
script = ExtResource("2_o70ro")
ui_resource = SubResource("Resource_r43o2")
ball_type = 1
cooldown = 15.0
acceleration_multiplier = 0.3
damage_color = Color(1, 0.564706, 0, 1)

[node name="AttributeComponent" type="Node" parent="."]
script = ExtResource("3_jfk08")
attribute_set = SubResource("Resource_obbgq")
metadata/_custom_type_script = "uid://3lqxnj5nr7f1"

[node name="CollisionShape2D" type="CollisionShape2D" parent="."]
shape = SubResource("CircleShape2D_yi17e")

[node name="Sprite2D" type="Sprite2D" parent="."]
material = SubResource("ShaderMaterial_pwu0t")
scale = Vector2(0.4, 0.4)
texture = ExtResource("4_d6hjy")

[node name="Trail" type="Line2D" parent="Sprite2D"]
top_level = true
z_index = -1
texture_repeat = 2
material = SubResource("ShaderMaterial_hvdpe")
width = 20.0
width_curve = SubResource("Curve_yi17e")
texture_mode = 2
joint_mode = 2
begin_cap_mode = 2
end_cap_mode = 2
script = ExtResource("4_o70ro")
max_points = 35

[node name="GPUParticles2D" type="GPUParticles2D" parent="Sprite2D"]
material = SubResource("CanvasItemMaterial_mspxm")
scale = Vector2(2.5, 2.5)
amount = 40
texture = ExtResource("7_o70ro")
lifetime = 0.6
process_material = SubResource("ParticleProcessMaterial_lbb5j")

[node name="Abilities" type="Node" parent="."]
script = ExtResource("3_d6hjy")

[node name="FireAbility" type="Node" parent="Abilities"]
script = ExtResource("3_d6hjy")
debuff_template = ExtResource("13_l38oo")
ui_resource = SubResource("Resource_2sac1")
