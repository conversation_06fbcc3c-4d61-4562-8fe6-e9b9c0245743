shader_type canvas_item;

// New gradient control parameters
uniform vec4 gradient_color : source_color = vec4(0.0, 0.0, 0.0, 1.0);
uniform float gradient_solid_ratio : hint_range(0.0, 1.0) = 0.2;
uniform float gradient_transition_ratio : hint_range(0.0, 1.0) = 0.5;

// UV offset parameter
instance uniform vec2 texture_uv_offset = vec2(0.0, 0.0);
instance uniform vec2 dirt_uv_offset = vec2(0.0, 0.0);

// Dirt/Stain effect parameters
uniform sampler2D noise_texture;
uniform float dirt_strength : hint_range(0.0, 1.0) = 0.3;
uniform vec2 dirt_scale = vec2(1.0, 1.0);
uniform vec3 dirt_color : source_color = vec3(0.2, 0.15, 0.1);
uniform float dirt_threshold : hint_range(0.0, 1.0) = 0.5;
uniform bool use_anti_tile = true; // 启用抗平铺功能

varying vec2 vertex_pos;
varying flat float v_face_id; // Use flat to prevent interpolation
varying float v_normalized_y; // Normalized Y coord for non-repeating gradients
varying float v_side_gradient; // Gradient for side faces

// -- 抗平铺旋转模式函数 --
// 2D 旋转矩阵
mat2 rot(float a) {
    float c = cos(a);
    float s = sin(a);
    return mat2(vec2(c, s), vec2(-s, c));
}

// 伪随机数生成器 (哈希函数)
float rand(vec2 p, float seed) {
    return fract(sin(dot(p, vec2(12.9898, 78.233)) + seed) * 43758.5453123);
}

// 通过混合多个随机旋转的采样来渲染纹理
vec4 texture_rotate(sampler2D tex, vec2 uv) {
    vec2 id = floor(uv);
    vec2 sv = smoothstep(0.0, 1.0, fract(uv));
    float seed = 9.78376; // 随机种子

    // 为当前及相邻瓦片生成随机旋转角度
    float self_rot = rand(id, seed) * 6.28318; // 2 * PI
    float right_rot = rand(id + vec2(1.0, 0.0), seed) * 6.28318;
    float top_rot = rand(id + vec2(0.0, 1.0), seed) * 6.28318;
    float top_right_rot = rand(id + vec2(1.0, 1.0), seed) * 6.28318;

    vec2 dx = dFdx(uv);
    vec2 dy = dFdy(uv);

    // 对四个角的瓦片进行独立旋转采样
    vec4 s1 = textureGrad(tex, fract(uv * rot(self_rot)), dx, dy);
    vec4 s2 = textureGrad(tex, fract(uv * rot(right_rot)), dx, dy);
    vec4 s3 = textureGrad(tex, fract(uv * rot(top_rot)), dx, dy);
    vec4 s4 = textureGrad(tex, fract(uv * rot(top_right_rot)), dx, dy);

    // 双线性插值，平滑混合四个采样结果
    return mix(mix(s1, s2, sv.x), mix(s3, s4, sv.x), sv.y);
}

void vertex() {
	// Pass vertex position to fragment shader for noise calculation
	vertex_pos = VERTEX.xy;
	// Decode face ID here from the per-vertex color and pass to fragment shader
	v_face_id = float(round(COLOR.b * 3.0));
	// Pass normalized Y from R channel
	v_normalized_y = COLOR.r;
	// Pass side gradient from G channel
	v_side_gradient = COLOR.g;
}

void fragment() {
	bool has_tex = (TEXTURE_PIXEL_SIZE.x + TEXTURE_PIXEL_SIZE.y) > 0.0;
	int face = int(v_face_id); // Convert from float varying to int

	vec4 tex_col;
	if (has_tex) {
		vec2 offset_uv = UV + texture_uv_offset;
		tex_col = texture(TEXTURE, offset_uv);

		// Apply dirt effect if noise texture is provided
		if (dirt_strength > 0.0) {
			vec2 noise_uv;
			// 使用顶点坐标生成噪声 UV，从而避免贴图按单元格重复
			vec2 base_uv;
			if (face == 2) {           // Top 面：使用 xy
				base_uv = vertex_pos.xy * 0.01;
			} else if (face == 1) {    // Front 垂直面：使用 xy
				base_uv = vertex_pos.xy * 0.01;
			} else {                   // Side/Inner：交换 xy 方向增加变化
				base_uv = vertex_pos.yx * 0.01;
			}

			// 缩放并加入全局/面偏移
			noise_uv = base_uv * dirt_scale + dirt_uv_offset + vec2(float(face) * 7.13, float(face) * 3.17);

			// 使用旋转抗平铺技术采样噪声纹理
			float noise_value;
			if (use_anti_tile) {
				// 使用旋转抗平铺技术
				vec4 noise_sample = texture_rotate(noise_texture, noise_uv);
				noise_value = noise_sample.r;
			} else {
				// 常规平铺采样
				noise_value = texture(noise_texture, fract(noise_uv)).r;
			}

			float dirt_mask = smoothstep(dirt_threshold, dirt_threshold + 0.2, noise_value);
			dirt_mask = 1.0 - dirt_mask;

			vec3 dirt_effect = mix(tex_col.rgb, dirt_color, dirt_mask * dirt_strength);
			tex_col.rgb = dirt_effect;
		}
	} else {
		tex_col = COLOR;
	}

	float tint_factor = COLOR.a;
	vec3 mixed_rgb = mix(tex_col.rgb, tex_col.rgb * COLOR.rgb, tint_factor);

	vec4 final_color = vec4(mixed_rgb, has_tex ? tex_col.a : COLOR.a);

	// Apply face-specific gradients
	float gradient_value = 0.0;
	bool apply_gradient = false;

	if (face == 0) { // Side faces
		gradient_value = v_side_gradient;
		apply_gradient = true;
	}
	else if (face == 1) { // Front/Bottom faces
		gradient_value = v_normalized_y;
		apply_gradient = true;
	}

	if (apply_gradient) {
		float edge1 = 1.0 - gradient_solid_ratio;
		float edge0 = edge1 - gradient_transition_ratio;

		float mix_factor = smoothstep(edge0, edge1, gradient_value);
		// Use the gradient color's alpha to control the mix strength.
		// This ensures that if the gradient color is transparent, it only reduces the gradient effect
		// instead of making the whole object transparent.
		final_color.rgb = mix(final_color.rgb, gradient_color.rgb, mix_factor * gradient_color.a);
	}

	COLOR = final_color;
}