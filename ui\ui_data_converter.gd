class_name UIDataConverter
extends RefCounted

## 升级数据转换工具类
##
## 负责将游戏实例数据转换为UI显示所需的数据格式
## 提供类型安全的数据转换和错误处理
## 支持富文本处理和占位符替换

## 将BallBase实例转换为BallUIData
## @param ball: 弹球实例
## @return: BallUIData实例，如果转换失败返回null
static func ball_to_ui_data(ball: BallBase) -> BallUIData:
	if not is_instance_valid(ball):
		push_error("UIDataConverter: 传入的弹球实例无效")
		return null
	if ball.ball_type == BallBase.BallType.NORMAL:
		return null

	# 获取UI资源信息
	var ui_resource: BallUIResource = ball.ui_resource
	if not ui_resource:
		push_warning("UIDataConverter: 弹球缺少UI资源，使用默认值")
		ui_resource = _create_default_ball_ui_resource()

	# 从UI资源获取基础信息
	var ball_name: String = ui_resource.ball_name
	var icon = ui_resource.get_icon_display()
	var ball_type: String = ""
	match ball.ball_type:
		BallBase.BallType.BASE:
			ball_type = ""
		BallBase.BallType.FUSION:
			ball_type = "融合"
		BallBase.BallType.EVOLUTION:
			ball_type = "进化"
		BallBase.BallType.MIX:
			ball_type = "进化x融合"

	# 使用RichTextProcessor生成描述和效果
	var description: String = RichTextProcessor.process_rich_text(ui_resource.description, ball)
	var effects: Array[String] = get_effect_summary(ball)

	return BallUIData.new(
		ball_name,
		description,
		icon,
		ball_type,
		ball.level,
		effects,
	)


## 将Relic实例转换为RelicUIData
## @param relic: 遗物实例
## @return: RelicUIData实例，如果转换失败返回null
static func relic_to_ui_data(relic: Relic) -> RelicUIData:
	if not is_instance_valid(relic):
		push_error("UIDataConverter: 传入的遗物实例无效")
		return null

	# 获取基础信息
	var relic_name: String = relic.name if relic.name != "" else "未知遗物"
	var description: String = RichTextProcessor.process_rich_text(relic.description, relic) if relic.description != "" else "暂无描述"
	var icon = relic.icon

	return RelicUIData.new(
		relic_name,
		description,
		icon,
		"common"
	)


## 获取当前游戏等级数据
## @return: LevelUIData实例
static func get_level_data() -> LevelUIData:
	if not GameManager:
		push_error("UIDataConverter: GameManager不可用")
		return LevelUIData.new()

	var current_level = GameManager.level
	var next_level = current_level + 1
	var current_exp = GameManager.experience
	var required_exp = GameManager.level_up_exp

	return LevelUIData.new(
		current_level,
		next_level,
		current_exp,
		required_exp
	)


## 验证弹球数据的完整性
## @param ball: 弹球实例
## @return: 是否有效
static func validate_ball_data(ball: BallBase) -> bool:
	if not is_instance_valid(ball):
		return false

	if not ball.attribute_component:
		push_warning("UIDataConverter: 弹球缺少AttributeComponent")
		return false

	return true


## 验证遗物数据的完整性
## @param relic: 遗物实例
## @return: 是否有效
static func validate_relic_data(relic: Relic) -> bool:
	if not is_instance_valid(relic):
		return false

	if relic.name == "":
		push_warning("UIDataConverter: 遗物缺少名称")
		return false

	return true


## 批量转换弹球数据（支持富文本和占位符）
## @param balls: 弹球实例数组
## @return: BallUIData数组
static func convert_balls_batch(balls: Array) -> Array[BallUIData]:
	var result: Array[BallUIData] = []

	for ball in balls:
		if ball is BallBase:
			var ui_data: BallUIData = ball_to_ui_data(ball as BallBase)
			if ui_data:
				result.append(ui_data)
		else:
			push_warning("UIDataConverter: 数组中包含非BallBase类型的对象")

	return result


## 批量转换遗物数据
## @param relics: 遗物实例数组
## @return: RelicUIData数组
static func convert_relics_batch(relics: Array[Relic]) -> Array[RelicUIData]:
	var result: Array[RelicUIData] = []

	for relic in relics:
		var ui_data: RelicUIData = relic_to_ui_data(relic)
		if ui_data:
			result.append(ui_data)

	return result


## 创建默认的弹球UI资源
## @return: 默认的BallUIResource实例
static func _create_default_ball_ui_resource() -> BallUIResource:
	var resource = BallUIResource.new()
	resource.ball_name = "未知弹球"
	resource.description = "暂无描述"
	resource.icon_text = "⚽"
	resource.ball_type = "normal"
	resource.rarity = "普通"
	return resource


## 获取弹球的简短效果列表（用于UI显示）
## @param ball: 弹球实例
## @return: 效果描述数组
static func get_effect_summary(ball: BallBase) -> Array[String]:
	var effects: Array[String] = []
	# 添加能力效果
	effects.append_array(extract_ability_descriptions(ball))

	return effects


## 从弹球的能力系统中提取能力描述
## @param ball: 弹球实例
## @return: 能力描述数组
static func extract_ability_descriptions(ball: BallBase) -> Array[String]:
	var descriptions: Array[String] = []

	if not is_instance_valid(ball) or not ball.abilities:
		return descriptions

	# 遍历所有能力节点
	for child in ball.abilities.get_children():
		var ability_desc: String = _extract_single_ability_description(child)
		if ability_desc != "":
			descriptions.append(ability_desc)

	return descriptions


## 提取单个能力的详细描述
## @param ability: 能力节点
## @return: 格式化的能力描述
static func _extract_single_ability_description(ability: BallAbilityBase) -> String:
	if not is_instance_valid(ability):
		return ""

	var ui_resource: AbilityUIResource = ability.ui_resource
	return RichTextProcessor.process_rich_text(ui_resource.description, ability)