[gd_resource type="ShaderMaterial" load_steps=3 format=3 uid="uid://yuj6qn0q5oh5"]

[ext_resource type="Shader" uid="uid://dp1bi6cslkakl" path="res://assets/materials/shader/flake_ball.gdshader" id="1_xcdjx"]
[ext_resource type="Texture2D" uid="uid://u5ndsxu34wr3" path="res://assets/imgs/balls/128x128.png" id="2_xcdjx"]

[resource]
shader = ExtResource("1_xcdjx")
shader_parameter/iChannel0 = ExtResource("2_xcdjx")
shader_parameter/iResolution = Vector2(1024, 1024)
shader_parameter/background_transparent = true
shader_parameter/time_scale = 0.5
shader_parameter/sphere_color = Color(0.282026, 0.118012, 0.154599, 1)
shader_parameter/displacement_color = Color(1, 1, 1, 1)
shader_parameter/sphere_radius = 4.485
shader_parameter/displacement_scale = 5.0
shader_parameter/light_intensity = 0.6
shader_parameter/reflection_intensity = 1.555
shader_parameter/refraction_intensity = 1.385
shader_parameter/refraction_angle = 0.7
shader_parameter/base_brightness = 0.35
shader_parameter/specular_power = 16.0
shader_parameter/displacement_blend = 0.46
shader_parameter/hash_speed = 0.6
