shader_type canvas_item;
render_mode blend_mix;

// 使用 'varying' 将顶点着色器中的原始粒子颜色传递给片元着色器。
// 这样我们就可以在片元阶段使用未被纹理颜色影响的纯净粒子颜色。
varying vec4 particle_color;

void vertex() {
	// 在顶点阶段，COLOR 变量是粒子自身的颜色，我们把它存入 varying 变量中。
	particle_color = COLOR;
}

void fragment() {
	// 从纹理的红色通道获取亮度值，并将其用作 Alpha（透明度）。
	// 这样就实现了“黑底变透明，白底变不透明”的效果。
	float alpha = texture(TEXTURE, UV).r;

	// 设置最终输出的颜色：
	// - RGB 颜色：使用我们从顶点着色器传过来的、纯净的粒子颜色。
	// - Alpha 透明度：使用粒子自己的透明度与我们从纹理计算出的透明度相乘。
	COLOR = vec4(particle_color.rgb, particle_color.a * alpha);
}
