[gd_resource type="ShaderMaterial" load_steps=2 format=3 uid="uid://bnshtdvmq5lpf"]

[ext_resource type="Shader" uid="uid://bionbfobjqg6w" path="res://assets/materials/shader/radiation_ball.gdshader" id="1_jw4eo"]

[resource]
resource_local_to_scene = true
shader = ExtResource("1_jw4eo")
shader_parameter/time_scale = 1.0
shader_parameter/time_offset = 0.0
shader_parameter/core_color = Color(1.3, 0.8, 0.3, 1)
shader_parameter/edge_color = Color(1.1, 0.3, 0.1, 1)
shader_parameter/size_scale = 1.63
shader_parameter/edge_softness = 1.0
shader_parameter/core_size = 1.181
shader_parameter/edge_size = 0.0
shader_parameter/noise_scale = 40.0
shader_parameter/noise_strength = 0.676
