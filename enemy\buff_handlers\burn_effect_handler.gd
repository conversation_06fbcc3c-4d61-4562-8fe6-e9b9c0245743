class_name BurnEffectHandler
extends BuffHandlerBase

## 将灼烧需要的资源暴露出来，方便在编辑器里配置
@export var burn_material: ShaderMaterial
@export var burn_to_die_material: ShaderMaterial
@export var damage_color: Color = Color.WHITE

# 实现具体的添加逻辑
func on_buff_added(target: Node, buff: AttributeBuff):
	if not is_instance_valid(target) or not is_instance_valid(buff):
		return
	
	target.sprite.add_material(handled_buff_name, burn_material)
	target.sprite.set_instance_shader_param_by_name(handled_buff_name, "noise_seed", Vector2(randf() * 10, randf() * 10))
	
	# 连接DOT信号，我们只绑定target，因为buff会由信号自己传来
	if buff is AttributeBuffDOT and not buff.is_connected("dot_triggered", Callable(self, "on_dot_triggered")):
		buff.dot_triggered.connect(Callable(self, "on_dot_triggered").bind(target))

# 实现具体的移除逻辑
func on_buff_removed(target: Node, buff: AttributeBuff):
	if not is_instance_valid(target) or not is_instance_valid(buff):
		return
	
	target.sprite.erase_material(handled_buff_name)

func handle_enemy_death_effect(target: EnemyBase) -> bool:
	if not is_instance_valid(target) or not burn_to_die_material:
		return false
		
	var sprite = target.sprite
	sprite.add_material("burn_to_die", burn_to_die_material)
	sprite.set_instance_shader_param_by_name("burn_to_die", "progress", -1.0)
	
	var tween = target.create_tween()
	tween.tween_method(func(value: float): sprite.set_instance_shader_param_by_name("burn_to_die", "progress", value), -1.0, 2.0, 1.5)
	tween.finished.connect(func(): target.queue_free())
	
	return true

# 实现具体的DOT触发逻辑
# 签名现在接收3个参数：2个来自信号emit，1个来自bind
func on_dot_triggered(attribute: Attribute, buff: AttributeBuff, target: Node):
	if not is_instance_valid(target) or not target.has_method("take_damage"):
		return
	
	target.take_damage(buff.stack_value, damage_color, self, true)
