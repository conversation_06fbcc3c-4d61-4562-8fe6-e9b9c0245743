extends Node


@onready var label: RichTextLabel = $RichTextLabel
@onready var item_slot: ItemSlot = $ItemSlot
@onready var item_slot2: ItemSlot = $ItemSlot2

func _ready() -> void:
	var ball = preload("res://ball/prefab/poison_ball.tscn")
	var ball_instance = ball.instantiate()
	add_child(ball_instance)
	var ui_data = UIDataConverter.ball_to_ui_data(ball_instance)
	print("测试：单个弹球转换结果")
	print(ui_data.description)
	label.append_text(ui_data.description)
	item_slot.set_item_data(ui_data)

	var iron_armor = preload("res://relic/relics/iron_armor.tres")
	var ui_data2 = UIDataConverter.relic_to_ui_data(iron_armor)
	item_slot2.set_item_data(ui_data2)
	print("测试结束")
