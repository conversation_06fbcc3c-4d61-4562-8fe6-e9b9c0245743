[gd_scene load_steps=19 format=3 uid="uid://cos2xoxtwvbjn"]

[ext_resource type="PhysicsMaterial" uid="uid://d0d4n7iiiu1o8" path="res://assets/materials/physics/ball.tres" id="1_cqd61"]
[ext_resource type="Script" uid="uid://drjqkf3mt3gs1" path="res://ball/ball_base.gd" id="2_dlwab"]
[ext_resource type="Script" uid="uid://3lqxnj5nr7f1" path="res://addons/attribute_manager/AttributeComponent.gd" id="3_0x7nb"]
[ext_resource type="Script" uid="uid://coux00joxi7h1" path="res://addons/attribute_manager/Attribute.gd" id="4_5a5b0"]
[ext_resource type="Script" uid="uid://qu5oshh0ccl5" path="res://attribute/ball_damage_attribute.gd" id="5_trot1"]
[ext_resource type="Script" uid="uid://ucf5nur2irtk" path="res://attribute/scored_attribute.gd" id="6_f1o5x"]
[ext_resource type="Script" uid="uid://2dxckbgqoga5" path="res://addons/attribute_manager/AttributeSet.gd" id="7_7kc6j"]
[ext_resource type="Texture2D" uid="uid://donaevmmjeptc" path="res://assets/imgs/balls/普通球.png" id="8_rdmac"]
[ext_resource type="Script" uid="uid://c4hm0ggsbq3bl" path="res://addons/trail/trail.gd" id="9_kapgh"]

[sub_resource type="Resource" id="Resource_h1k6s"]
script = ExtResource("4_5a5b0")
base_value = 5.0
can_cache = true
metadata/_custom_type_script = "uid://coux00joxi7h1"

[sub_resource type="Resource" id="Resource_dcquf"]
script = ExtResource("5_trot1")
min_value_source = "min_damage"
max_value_source = "max_damage"
crit_rate_source = "crit"
base_value = 0.0
can_cache = true
metadata/_custom_type_script = "uid://qu5oshh0ccl5"

[sub_resource type="Resource" id="Resource_5s6o1"]
script = ExtResource("6_f1o5x")
growth_per_score = 0.002
operate_type = 0
max_value = 9999.0
min_value = -9999.0
base_value = 5.0
can_cache = true
metadata/_custom_type_script = "uid://ucf5nur2irtk"

[sub_resource type="Resource" id="Resource_nywuv"]
script = ExtResource("6_f1o5x")
growth_per_score = 0.001
operate_type = 0
max_value = 9999.0
min_value = -9999.0
base_value = 2.0
can_cache = true
metadata/_custom_type_script = "uid://ucf5nur2irtk"

[sub_resource type="Resource" id="Resource_qsmmo"]
script = ExtResource("4_5a5b0")
base_value = 600.0
can_cache = true
metadata/_custom_type_script = "uid://coux00joxi7h1"

[sub_resource type="Resource" id="Resource_mhi2s"]
resource_local_to_scene = true
script = ExtResource("7_7kc6j")
attributes = Dictionary[StringName, ExtResource("4_5a5b0")]({
&"crit": SubResource("Resource_h1k6s"),
&"damage": SubResource("Resource_dcquf"),
&"max_damage": SubResource("Resource_5s6o1"),
&"min_damage": SubResource("Resource_nywuv"),
&"speed": SubResource("Resource_qsmmo")
})
metadata/_custom_type_script = "uid://2dxckbgqoga5"

[sub_resource type="Curve" id="Curve_yi17e"]
_data = [Vector2(0, 0), 0.0, 0.0, 0, 0, Vector2(1, 1), 0.0, 0.0, 0, 0]
point_count = 2

[sub_resource type="Gradient" id="Gradient_yi17e"]
colors = PackedColorArray(1, 1, 1, 0, 1, 1, 1, 0.555)

[sub_resource type="CircleShape2D" id="CircleShape2D_yi17e"]
radius = 13.0

[node name="NormalBall" type="RigidBody2D"]
collision_layer = 2
collision_mask = 13
physics_material_override = ExtResource("1_cqd61")
gravity_scale = 0.0
can_sleep = false
lock_rotation = true
linear_damp_mode = 1
angular_damp_mode = 1
script = ExtResource("2_dlwab")
cooldown = 15.0
acceleration_multiplier = 0.1

[node name="AttributeComponent" type="Node" parent="."]
script = ExtResource("3_0x7nb")
attribute_set = SubResource("Resource_mhi2s")
metadata/_custom_type_script = "uid://3lqxnj5nr7f1"

[node name="Sprite2D" type="Sprite2D" parent="."]
scale = Vector2(0.24, 0.24)
texture = ExtResource("8_rdmac")

[node name="Trail" type="Line2D" parent="Sprite2D"]
top_level = true
z_index = -1
points = PackedVector2Array(0, 0)
width = 12.0
width_curve = SubResource("Curve_yi17e")
gradient = SubResource("Gradient_yi17e")
joint_mode = 2
script = ExtResource("9_kapgh")
max_points = 25

[node name="CollisionShape2D" type="CollisionShape2D" parent="."]
shape = SubResource("CircleShape2D_yi17e")

[node name="Abilities" type="Node" parent="."]
