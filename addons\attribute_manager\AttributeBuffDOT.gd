@tool
class_name AttributeBuffDOT extends AttributeBuff

signal dot_triggered(attribute: Attribute, buff: AttributeBuff)

## DOT的周期
var period: float
var _initialized: bool = false


var cycle_time: float = 0.0

func _init():
	super._init()
	if not attribute_set.has_attribute("period"):
		attribute_set.add_attribute("period", Attribute.create(1.0))


func run_process(delta: float):
	_try_to_trigger_dot(delta)
	
	if is_pending_remove:
		return
	
	super.run_process(delta)


func _try_to_trigger_dot(delta: float):
	if not _initialized:
		period = attribute_set.find_attribute("period").get_value()
		_initialized = true
		
	cycle_time += delta
	if cycle_time >= period:
		cycle_time = fmod(cycle_time, period)
		apply_to_attribute()


func apply_to_attribute():
	if is_instance_valid(applied_attribute):
		applied_attribute.apply_buff_operation(self)
		dot_triggered.emit(applied_attribute, self)


func get_attribute_value(attribute_name: String) -> float:
	return attribute_set.find_attribute(attribute_name).get_value()


func set_attribute_value(attribute_name: String, value: float):
	attribute_set.find_attribute(attribute_name).set_value(value)
