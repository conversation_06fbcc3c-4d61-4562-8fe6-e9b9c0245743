[gd_resource type="Resource" script_class="BloodEffectHandler" load_steps=4 format=3 uid="uid://ct3wr54afncsw"]

[ext_resource type="Material" uid="uid://clg67pm3b1u8i" path="res://ball/effect/blood_effect/blood_debuff_material.tres" id="1_morpv"]
[ext_resource type="Script" uid="uid://c05qkjgta4083" path="res://enemy/buff_handlers/blood_effect_handler.gd" id="1_wh01u"]
[ext_resource type="Material" uid="uid://tgj0kdb4mduw" path="res://ball/effect/blood_effect/blood_to_die_material.tres" id="2_p63d0"]

[resource]
resource_local_to_scene = true
script = ExtResource("1_wh01u")
blood_material = ExtResource("1_morpv")
blood_to_die_material = ExtResource("2_p63d0")
damage_color = Color(0.61, 0, 0, 1)
handled_buff_name = "blood"
metadata/_custom_type_script = "uid://c05qkjgta4083"
