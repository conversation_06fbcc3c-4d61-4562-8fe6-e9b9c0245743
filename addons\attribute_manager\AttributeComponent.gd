class_name AttributeComponent
extends Node

## 属性集，注意勾选Local to Scene。否则实例化对象会公用一个属性集
@export var attribute_set: AttributeSet


func _physics_process(delta: float) -> void:
	if is_instance_valid(attribute_set):
		attribute_set.run_process(delta)


#region 外部函数
func get_attribute_value(attribute_name: String, consume_buffs: bool = false, consume_buffs_dependencies: bool = false) -> float:
	if not is_instance_valid(attribute_set):
		return 0.0
	var attribute: Attribute = attribute_set.find_attribute(attribute_name)
	return attribute.get_value(consume_buffs, consume_buffs_dependencies)


func has_attribute(attribute_name: String) -> bool:
	return attribute_set.has_attribute(attribute_name)


func find_attribute(attribute_name: String) -> Attribute:
	return attribute_set.find_attribute(attribute_name) if is_instance_valid(attribute_set) else null


func set_attribute_value(attribute_name: String, value: float) -> void:
	if not is_instance_valid(attribute_set):
		return
	var attribute: Attribute = find_attribute(attribute_name)
	attribute.set_value(value)

#endregion
