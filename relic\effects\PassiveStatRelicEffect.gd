class_name PassiveStatRelicEffect extends RelicEffect

## 常驻数值被动效果

## 要修改的属性名称
@export var attribute_name: StringName
## 要应用的Buff资源
@export var buff: AttributeBuff

var _applied_buff_instance: AttributeBuff


func on_acquired(target: Node):
	var attribute_comp = target.get_node("AttributeComponent") as AttributeComponent
	if not is_instance_valid(attribute_comp):
		push_error("Relic target '%s' does not have an AttributeComponent." % target.name)
		return

	if attribute_comp.has_attribute(String(attribute_name)):
		var attribute = attribute_comp.find_attribute(String(attribute_name))
		_applied_buff_instance = attribute.add_buff(buff)
	else:
		push_warning("Attribute '%s' not found on target '%s' for relic effect." % [attribute_name, target.name])


func on_lost(target: Node):
	if not is_instance_valid(_applied_buff_instance):
		return # 没有已应用的Buff实例，无需操作

	var attribute_comp = target.get_node("AttributeComponent")
	if not is_instance_valid(attribute_comp):
		return # 目标已失效或没有组件，无法移除

	if attribute_comp.attribute_set.has_attribute(String(attribute_name)):
		var attribute = attribute_comp.attribute_set.find_attribute(String(attribute_name))
		attribute.remove_buff(_applied_buff_instance)


## 获取被动状态遗物效果的 AttributeSet（统一接口实现）
## @return: buff 的 AttributeSet 实例
func get_attribute_set() -> AttributeSet:
	if is_instance_valid(buff):
		return buff.attribute_set
	return null
