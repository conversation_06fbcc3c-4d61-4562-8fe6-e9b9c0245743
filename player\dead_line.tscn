[gd_scene load_steps=4 format=3 uid="uid://bm84kvlt27pga"]

[ext_resource type="Script" uid="uid://dayg1nte5a4y6" path="res://scene/dead_line.gd" id="1_idb5s"]
[ext_resource type="Texture2D" uid="uid://npe7tklubw7k" path="res://assets/imgs/texture/T_2_2_Wood_basecolor.png" id="2_idb5s"]

[sub_resource type="WorldBoundaryShape2D" id="WorldBoundaryShape2D_tipki"]

[node name="DeadLine" type="Area2D"]
collision_mask = 22
script = ExtResource("1_idb5s")

[node name="CollisionShape2D" type="CollisionShape2D" parent="."]
position = Vector2(360, 1093)
shape = SubResource("WorldBoundaryShape2D_tipki")
one_way_collision_margin = 128.0

[node name="Line" type="Sprite2D" parent="."]
position = Vector2(360, 1090)
scale = Vector2(1.5, 0.02)
texture = ExtResource("2_idb5s")
