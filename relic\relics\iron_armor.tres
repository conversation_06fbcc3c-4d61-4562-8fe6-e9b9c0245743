[gd_resource type="Resource" script_class="Relic" load_steps=16 format=3 uid="uid://dbp2pc7pkvmck"]

[ext_resource type="Script" uid="uid://coux00joxi7h1" path="res://addons/attribute_manager/Attribute.gd" id="1_mulde"]
[ext_resource type="Script" uid="uid://5ww2mn76prh4" path="res://attribute/leveled_attribute.gd" id="2_2w5ad"]
[ext_resource type="Script" uid="uid://2dxckbgqoga5" path="res://addons/attribute_manager/AttributeSet.gd" id="3_afvoh"]
[ext_resource type="Script" uid="uid://c5vncve4pivd5" path="res://addons/attribute_manager/AttributeBuff.gd" id="4_7iyqx"]
[ext_resource type="Script" uid="uid://ddpoubqealq4l" path="res://relic/effects/PassiveStatRelicEffect.gd" id="5_rt4a1"]
[ext_resource type="Texture2D" uid="uid://cbgp0ihh0438x" path="res://ui/img/64x64.png" id="6_1gbia"]
[ext_resource type="Script" uid="uid://crf0ktn1ipx6t" path="res://relic/Relic.gd" id="7_jhx0c"]

[sub_resource type="Resource" id="Resource_3gspy"]
script = ExtResource("1_mulde")
base_value = 0.0
can_cache = true

[sub_resource type="Resource" id="Resource_1gbia"]
script = ExtResource("1_mulde")
base_value = 1.0
can_cache = true
metadata/_custom_type_script = "uid://coux00joxi7h1"

[sub_resource type="Resource" id="Resource_cdhvy"]
script = ExtResource("1_mulde")
base_value = 1.0
can_cache = true

[sub_resource type="Resource" id="Resource_xuvt5"]
script = ExtResource("2_2w5ad")
growth_per_level = 10.0
base_value = 10.0
can_cache = true
metadata/_custom_type_script = "uid://5ww2mn76prh4"

[sub_resource type="Resource" id="Resource_ulmo1"]
resource_local_to_scene = true
script = ExtResource("3_afvoh")
attributes = Dictionary[StringName, ExtResource("1_mulde")]({
&"duration": SubResource("Resource_3gspy"),
&"level": SubResource("Resource_1gbia"),
&"max_stacks": SubResource("Resource_cdhvy"),
&"value": SubResource("Resource_xuvt5")
})

[sub_resource type="Resource" id="Resource_o0c6h"]
resource_local_to_scene = true
script = ExtResource("4_7iyqx")
buff_name = "damage_reduction"
operation = 0
policy = 0
merging = 2
attribute_set = SubResource("Resource_ulmo1")
metadata/_custom_type_script = "uid://c5vncve4pivd5"

[sub_resource type="Resource" id="Resource_4jwpk"]
script = ExtResource("5_rt4a1")
attribute_name = &"damage_reduction"
buff = SubResource("Resource_o0c6h")
metadata/_custom_type_script = "uid://d20dks5ay4dme"

[sub_resource type="AtlasTexture" id="AtlasTexture_b1qyl"]
atlas = ExtResource("6_1gbia")
region = Rect2(0, 7680, 64, 64)

[resource]
resource_local_to_scene = true
script = ExtResource("7_jhx0c")
name = "铁甲"
description = "受到伤害降低 [value]%"
icon = SubResource("AtlasTexture_b1qyl")
effect = SubResource("Resource_4jwpk")
metadata/_custom_type_script = "uid://crf0ktn1ipx6t"
