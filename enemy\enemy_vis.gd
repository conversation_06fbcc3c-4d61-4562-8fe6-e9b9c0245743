@tool
extends Node2D
class_name EnemyVis

signal shake_finished
# 编辑器按钮：生成随机格子
@export_tool_button("generate_grid") var generate_grid_btn: Callable = _generate_grid

# 编辑器按钮：为已有格子随机放置敌人
@export_tool_button("place_enemies") var place_enemies_btn: Callable = _place_enemies_on_existing_grid

@onready var box_shape: BoxShape = $BoxShape
# 我们将使用 Control 作为遮罩，因为它专门支持子节点裁剪功能。
# Control节点的clip_children属性在运行时和编辑器中行为一致。
@onready var mask_clipper: Control = $MaskClipper

# 场景预制体数组，用于存放不同敌人类型
@export var enemy_unit_scenes: Array[PackedScene] = []
@export var auto_scale_to_cell: bool = true
@export var min_cells: int = 1
@export var max_cells: int = 4

@export_range(1, 2) var row_count: int = 2

var enemy_array: Array[AnimatedSprite2D] = []
# 存储初始状态以防止动画累加错误
var _box_initial_pos: Vector2
var _box_initial_rot: float
var _enemies_initial_pos := {}

# 怪物宽度收缩参数，1.0为完全填充格子，小于1.0会有边距
@export_range(0.1, 2.0, 0.01) var monster_scale_factor: float = 1.2:
	set(value):
		monster_scale_factor = value
		# 如果已经放置了敌人，重新调整它们的大小
		if box_shape:
			_adjust_enemies_scale()


# 在已有的格子配置上重新随机放置敌人
func _place_enemies_on_existing_grid() -> void:
	_clear_visuals()

	# 获取当前的格子配置
	var grid_config: Array[Variant] = [box_shape.row_1, box_shape.row_2, box_shape.row_3]

	# 在已有格子上放置随机敌人
	_place_random_enemies(grid_config)


# 重新调整所有敌人的缩放比例
func _adjust_enemies_scale() -> void:
	if not box_shape:
		return

	var cell_size: Vector2 = box_shape.cell_size

	for child in mask_clipper.get_children():
		if child != box_shape and (child is Sprite2D or child is AnimatedSprite2D):
			_adjust_enemy_scale(child, cell_size)

	# 重新计算遮罩
	_create_or_update_mask()


# 调整单个敌人的缩放以适应格子大小
func _adjust_enemy_scale(enemy_node: Node2D, cell_size: Vector2) -> void:
	if not (enemy_node is Sprite2D or enemy_node is AnimatedSprite2D):
		return

	var natural_size := Vector2.ZERO

	# 获取敌人精灵的自然大小
	if enemy_node is Sprite2D:
		if enemy_node.texture:
			natural_size = enemy_node.texture.get_size()
	elif enemy_node is AnimatedSprite2D:
		if enemy_node.sprite_frames and enemy_node.animation != "":
			var frame_count = enemy_node.sprite_frames.get_frame_count(enemy_node.animation)
			if frame_count > 0:
				var texture = enemy_node.sprite_frames.get_frame_texture(enemy_node.animation, 0)
				if texture:
					natural_size = texture.get_size()

	# 如果无法获取大小，不做处理
	if natural_size.x <= 0 or natural_size.y <= 0:
		return

	# -- 新逻辑: 基于格子宽度计算缩放，使其能覆盖边框 --
	# 1. 计算基础缩放比例，使敌人宽度与格子宽度相同
	var _scale = cell_size.x / natural_size.x

	# 2. 应用 monster_scale_factor，允许用户调整最终大小
	#    当 monster_scale_factor > 1 时，敌人会比格子宽
	_scale *= monster_scale_factor

	# 3. 应用缩放，保持宽高比
	enemy_node.scale = Vector2(_scale, _scale)


func _ready() -> void:
	# 使用Control节点作为遮罩容器，它专门支持子节点裁剪功能。
	# 设置裁剪模式为CLIP_CHILDREN_ONLY，使子节点被裁剪到父节点边界内。
	# Control节点在运行时和编辑器中的clip_children行为一致。
	if mask_clipper:
		mask_clipper.clip_children = CanvasItem.CLIP_CHILDREN_ONLY

	# 记录 BoxShape 的初始状态
	if box_shape:
		_box_initial_pos = box_shape.position
		_box_initial_rot = box_shape.rotation


# -------------------- Private Helper Functions --------------------

# 清理所有生成的视觉元素（敌人和遮罩）
func _clear_visuals() -> void:
	# 清理可能存在的、作为直接子节点的敌人
	for child in get_children():
		if child != box_shape and child != mask_clipper and (child is Sprite2D or child is AnimatedSprite2D):
			child.queue_free()
	# 清理位于遮罩裁剪器内的敌人
	for child in mask_clipper.get_children():
		child.queue_free()

	enemy_array.clear()
	_enemies_initial_pos.clear()

	# 重置遮罩区域。
	if mask_clipper:
		# Control节点不需要occluder属性，通过size属性控制裁剪区域
		pass


# 将格子配置应用到BoxShape节点
func _apply_grid_config_to_box_shape(grid_config: Array[int]) -> void:
	if not box_shape:
		push_error("在 _apply_grid_config_to_box_shape 中未找到BoxShape节点")
		return
	box_shape.row_count = self.row_count
	box_shape.row_1 = grid_config[0]
	box_shape.row_2 = grid_config[1] if row_count > 1 else 0
	box_shape.row_3 = 0
	box_shape.update_texture_offset(Vector2(randf_range(-1.0, 1.0), randf_range(-1.0, 1.0)))


# -------------------- Public API & Tool Functions --------------------

# 根据预设的格子配置数据来生成形状
func generate_from_config(grid_config: Array[int], enemy_config: EnemySpawnConfig):
	_clear_visuals()
	_apply_grid_config_to_box_shape(grid_config)
	_place_random_enemies_with_config(grid_config, enemy_config)


func get_visual_bounds() -> Rect2:
	var total_rect = Rect2()
	var first_node: bool = true

	for child in get_children():
		var child_rect: Rect2
		if child.has_method("get_shape_rect"):
			# For custom-drawn nodes like Box
			child_rect = child.get_shape_rect()
			# get_shape_rect is local, need to offset by position
			child_rect.position += child.position
		elif child is Sprite2D:
			# For standard Sprite2D
			child_rect = child.get_rect()
			child_rect.position += child.position
		elif child is AnimatedSprite2D:
			# For AnimatedSprite2D
			var size = Vector2.ZERO
			if child.sprite_frames != null and child.animation != "":
				# 获取当前动画帧的纹理尺寸
				var frame_count = child.sprite_frames.get_frame_count(child.animation)
				if frame_count > 0:
					var texture = child.sprite_frames.get_frame_texture(child.animation, 0)
					if texture != null:
						size = texture.get_size() * child.scale
			child_rect = Rect2(-size / 2, size)
			child_rect.position += child.position
		elif child is CanvasItem and not child is Control:
			var size = child.get_rect().size
			child_rect = Rect2(-size / 2, size)
			child_rect.position += child.position
		else:
			continue

		if first_node:
			total_rect = child_rect
			first_node = false
		else:
			total_rect = total_rect.merge(child_rect)
	return total_rect


func get_shape_offset_in_bounds() -> Vector2:
	if not is_instance_valid(box_shape):
		return Vector2.ZERO

	# 1. 获取 BoxShape 自身在 EnemyVis 坐标系下的矩形
	var shape_rect: Rect2 = box_shape.get_shape_rect()
	shape_rect.position += box_shape.position

	# 2. 获取 EnemyVis 整体的视觉边界
	var bounds: Rect2 = get_visual_bounds()

	# 3. 计算并返回 BoxShape 左上角相对于整体边界左上角的偏移
	return shape_rect.position - bounds.position


func get_footprint_offset_from_visual_center() -> Vector2:
	if not is_instance_valid(box_shape):
		return Vector2.ZERO

	# 1. Get the grid config from the box shape
	var grid_config: Array[int] = [box_shape.row_1, box_shape.row_2, box_shape.row_3]

	# 2. Get metrics for the actual shape
	var metrics = get_grid_metrics(grid_config)
	if metrics.dimensions == Vector2i.ZERO:
		return Vector2.ZERO

	# 3. Calculate position of the top-left of the entire 3xN grid definition
	var cell_size: Vector2 = box_shape.cell_size
	var grid_top_left = - Vector2(cell_size.x * 3, cell_size.y * self.row_count) / 2.0

	# 4. Calculate position of the footprint's top-left corner (relative to EnemyVis origin)
	var footprint_top_left = grid_top_left + Vector2(metrics.min_col * cell_size.x, metrics.min_row * cell_size.y)

	# 5. Get the visual bounds of the entire EnemyVis content
	var visual_bounds: Rect2 = get_visual_bounds()

	# 6. Calculate the center of the visual bounds (relative to EnemyVis origin)
	var visual_center = visual_bounds.position + visual_bounds.size / 2.0

	# 7. The offset is the difference
	return footprint_top_left - visual_center


func _generate_grid() -> void:
	_clear_visuals()
	# 1. 随机生成连通格子配置
	var grid_config = generate_grid_config(min_cells, max_cells, row_count)
	# 2. 应用到Box
	_apply_grid_config_to_box_shape(grid_config)


# 随机生成敌人图形
func generate_random_enemy() -> void:
	_clear_visuals()
	var grid_config = generate_grid_config(min_cells, max_cells, row_count)
	_apply_grid_config_to_box_shape(grid_config)
	_place_random_enemies(grid_config)


# ==================== Static Utility Functions ====================

# 静态工具函数：根据给定的格子配置数据，计算其度量信息
static func get_grid_metrics(grid_config: Array[int]) -> Dictionary:
	var min_col: int = 3
	var max_col: int = -1
	var min_row: int = -1
	var max_row: int = -1

	for r in range(3):
		var row_flag: int = grid_config[r]
		if row_flag > 0:
			if min_row == -1:
				min_row = r
			max_row = r

			# 检查列 (从左到右: 0, 1, 2)
			if row_flag & 4: # Left
				min_col = min(min_col, 0)
				max_col = max(max_col, 0)
			if row_flag & 2: # Center
				min_col = min(min_col, 1)
				max_col = max(max_col, 1)
			if row_flag & 1: # Right
				min_col = min(min_col, 2)
				max_col = max(max_col, 2)

	if min_row == -1: # Empty shape
		return {"dimensions": Vector2i.ZERO, "min_row": - 1, "min_col": - 1}

	var width = max_col - min_col + 1
	var height = max_row - min_row + 1
	return {"dimensions": Vector2i(width, height), "min_row": min_row, "min_col": min_col}


# 静态工具函数：生成连通的随机格子配置
# 返回一个包含三个行配置的数组，每个元素是该行的位标志
static func generate_grid_config(_min_cells: int, _max_cells: int, _row_count: int = 2) -> Array[int]:
	var grid: Array[Variant] = []
	for i in range(_row_count):
		grid.append([0, 0, 0])
	var cell_count = randi_range(_min_cells, _max_cells)
	cell_count = min(cell_count, _row_count * 3)
	if cell_count == 0:
		return [0, 0, 0]
	var start_row = randi() % _row_count
	var start_col = randi() % 3
	grid[start_row][start_col] = 1
	var cells_filled: int = 1
	var frontier: Array = []
	var directions: Array[Variant] = [[0, 1], [1, 0], [0, -1], [-1, 0]]
	for dir in directions:
		var new_row = start_row + dir[0]
		var new_col = start_col + dir[1]
		if new_row >= 0 and new_row < _row_count and new_col >= 0 and new_col < 3:
			var neighbor: Array[Variant] = [new_row, new_col]
			if not frontier.has(neighbor):
				frontier.append(neighbor)
	while cells_filled < cell_count and not frontier.is_empty():
		var weighted_frontier: Array = []
		for cell in frontier:
			var weight: int = 0
			var r = cell[0]
			var c = cell[1]
			for dir in directions:
				var nr = r + dir[0]
				var nc = c + dir[1]
				if nr >= 0 and nr < _row_count and nc >= 0 and nc < 3 and grid[nr][nc] == 1:
					weight += 1
			for i in range(weight):
				weighted_frontier.append(cell)
		var next_cell: Array
		if not weighted_frontier.is_empty():
			next_cell = weighted_frontier[randi() % weighted_frontier.size()]
		elif not frontier.is_empty():
			next_cell = frontier[randi() % frontier.size()]
		else:
			break
		grid[next_cell[0]][next_cell[1]] = 1
		cells_filled += 1
		frontier.erase(next_cell)
		for dir in directions:
			var new_row = next_cell[0] + dir[0]
			var new_col = next_cell[1] + dir[1]
			if new_row >= 0 and new_row < _row_count and new_col >= 0 and new_col < 3 and grid[new_row][new_col] == 0:
				var neighbor: Array[Variant] = [new_row, new_col]
				if not frontier.has(neighbor):
					frontier.append(neighbor)
	var row_flags: Array[int] = [0, 0, 0]
	for r in range(_row_count):
		for c in range(3):
			if grid[r][c] == 1:
				var bit_value = int(pow(2, 2 - c))
				row_flags[r] |= bit_value
	while row_flags.size() < 3:
		row_flags.append(0)
	return row_flags


# 生成连通的随机格子配置
# 返回一个包含三个行配置的数组，每个元素是该行的位标志
func _generate_connected_grid(_min_cells: int, _max_cells: int) -> Array[int]:
	# 此函数的逻辑已移至静态函数 generate_grid_config
	return generate_grid_config(_min_cells, _max_cells)


# 在每个选中的格子上放置随机敌人
func _place_random_enemies(grid_config: Array, enemy_config: Array[PackedScene] = enemy_unit_scenes) -> void:
	if enemy_config.is_empty():
		push_warning("未配置敌人场景预制体")
		return

	# 计算格子大小
	var cell_size: Vector2 = box_shape.cell_size
	# 计算格子的起始位置（相对于box_shape的中心位置）
	var grid_origin = - Vector2(cell_size.x * 3, cell_size.y * 3) / 2.0
	# 遍历每一行
	for row in range(3):
		var row_config = grid_config[row]
		# 检查每一列
		for col in range(3):
			var bit_value = int(pow(2, 2 - col)) # 左(4)、中(2)、右(1)
			# 如果该格子被选中
			if row_config & bit_value:
				# 随机选择一个敌人场景
				var random_scene_index = randi() % enemy_config.size()
				var enemy_scene: PackedScene = enemy_config[random_scene_index]
				if enemy_scene:
					# 实例化敌人场景
					var enemy_instance: AnimatedSprite2D = enemy_scene.instantiate()
					# 计算格子中心位置
					var cell_position = grid_origin + Vector2(cell_size.x * (col + 0.5), cell_size.y * (row + 0.5))
					# 设置位置
					enemy_instance.position = cell_position
					# 添加为子节点
					add_child(enemy_instance)
					# 随机选择一个动画帧
					enemy_instance.frame = randi_range(0, enemy_instance.sprite_frames.get_frame_count(enemy_instance.animation) - 1)
					# 调整敌人缩放以适应格子大小
					if auto_scale_to_cell:
						_adjust_enemy_scale(enemy_instance, cell_size)

					enemy_array.append(enemy_instance)
					# 如果在编辑器中，设置为不可编辑
					if Engine.is_editor_hint():
						enemy_instance.set_owner(get_tree().edited_scene_root)
	_create_or_update_mask()

	# 敌人被重新挂载到遮罩节点后，记录其初始位置
	_enemies_initial_pos.clear()
	for enemy in enemy_array:
		if is_instance_valid(enemy):
			_enemies_initial_pos[enemy] = enemy.position


# 创建或更新底部边框遮罩
func _create_or_update_mask() -> void:
	if not box_shape or not box_shape.has_method("get_inner_polygons"):
		return

	var inner_polygons: Array[PackedVector2Array] = box_shape.get_inner_polygons()
	if inner_polygons.is_empty():
		# 如果没有内部多边形，则设置默认裁剪区域
		if mask_clipper:
			_set_default_mask_size()
		return

	# 使用 MaskGenerator 创建遮罩多边形
	var mask_polygons: Array[PackedVector2Array] = create_mask_from_polygons(inner_polygons, box_shape)
	if mask_polygons.is_empty():
		# 如果生成的遮罩为空，设置默认裁剪区域
		if mask_clipper:
			_set_default_mask_size()
		return

	# 根据多边形计算Control节点的位置和大小
	if not mask_polygons.is_empty():
		_update_control_mask_from_polygon(mask_polygons[0])

	# 将所有敌人移动到裁剪节点的子节点中
	for child in get_children():
		if child != box_shape and child != mask_clipper and (child is Sprite2D or child is AnimatedSprite2D):
			child.reparent(mask_clipper)


func create_mask_from_polygons(inner_polygons: Array[PackedVector2Array], parent_node: Node2D, expansion_size: float = 60.0) -> Array[PackedVector2Array]:
	if inner_polygons.is_empty():
		return []
	# 1. 对传入的每个 inner_polygon 做“向上左右”扩展。
	#    思路：
	#    1) 先使用 Geometry2D.offset_polygon 以 expansion_factor 向四周均匀扩展，
	#       这样可以一次性获得左右两侧的扩张效果；
	#    2) 之后整体将多边形再向上(−Y)平移 expansion_factor，
	#       抵消 offset_polygon 带来的向下(＋Y)扩张，使得最终效果
	#       仅在“上、左、右”三个方向产生扩张。
	#    3) 最后把结果转换到 parent_node(即 BoxShape 的父节点 EnemyVis) 的局部坐标系。
	var mask_polygons: Array[PackedVector2Array] = []

	# BoxShape→Parent 的 Transform2D
	var to_parent: Transform2D = parent_node.transform

	for poly in inner_polygons:
		if poly.is_empty():
			continue

		# a. 横向(左右)扩展：offset_polygon
		var offset_result: Array = Geometry2D.offset_polygon(poly, expansion_size)
		if offset_result.is_empty():
			continue

		for p in offset_result:
			if p is PackedVector2Array and not p.is_empty():
				var expanded_poly := PackedVector2Array()
				# b. 向上平移 expansion_factor
				for v in p:
					var moved_v: Vector2 = v + Vector2(0, -expansion_size)
					# c. 坐标系转换到 parent
					expanded_poly.append(to_parent * moved_v)
				mask_polygons.append(expanded_poly)

	return mask_polygons


# 设置默认的遮罩大小，当没有有效多边形时使用
func _set_default_mask_size() -> void:
	if not mask_clipper:
		return

	# 使用box_shape的大小作为默认裁剪区域
	if box_shape:
		var cell_size: Vector2 = box_shape.cell_size
		var default_size = Vector2(cell_size.x * 3, cell_size.y * row_count * 1.5)
		mask_clipper.size = default_size
		mask_clipper.position = - default_size / 2.0


# 根据多边形更新Control节点的位置和大小
func _update_control_mask_from_polygon(polygon: PackedVector2Array) -> void:
	if not mask_clipper or polygon.is_empty():
		return

	# 计算多边形的边界矩形
	var min_point = polygon[0]
	var max_point = polygon[0]

	for point in polygon:
		min_point.x = min(min_point.x, point.x)
		min_point.y = min(min_point.y, point.y)
		max_point.x = max(max_point.x, point.x)
		max_point.y = max(max_point.y, point.y)

	# 设置Control节点的位置和大小
	mask_clipper.position = min_point
	mask_clipper.size = max_point - min_point

# 震动
var _shake_tweens: Array[Tween] = []
var _active_shake_tweens_count := 0


func _on_shake_tween_finished():
	_active_shake_tweens_count -= 1
	if _active_shake_tweens_count <= 0:
		shake_finished.emit()


func shake(dir: Vector2, intensity: float = 1.0):
	# 停止并清理所有正在进行的震动动画
	for t in _shake_tweens:
		if t and t.is_valid():
			t.kill()

	_shake_tweens.clear()

	# 在开始新动画前，将所有节点重置到其初始状态
	if is_instance_valid(box_shape):
		box_shape.position = _box_initial_pos
		box_shape.rotation = _box_initial_rot

	for enemy in enemy_array:
		if is_instance_valid(enemy) and _enemies_initial_pos.has(enemy):
			enemy.position = _enemies_initial_pos[enemy]

	_active_shake_tweens_count = 0

	# 1. BoxShape Animation
	var box_tween = create_tween()
	_shake_tweens.append(box_tween)
	box_tween.finished.connect(_on_shake_tween_finished)
	_active_shake_tweens_count += 1

	var box_original_pos := box_shape.position
	var box_original_rot := box_shape.rotation

	# --- Pivot Correction Logic ---
	# 1. Get the visual bounding box of the shape in its local coordinates.
	var shape_rect := Rect2()
	if box_shape.has_method("get_shape_rect"):
		shape_rect = box_shape.get_shape_rect()

	# 2. Calculate the offset from the node's origin to its visual center.
	var visual_center_offset = shape_rect.position + shape_rect.size / 2.0
	# --- End of Pivot Correction Logic ---

	var bounce_height := 15.0 * intensity
	var rotation_amount := deg_to_rad(7.0) * intensity

	# -- New Rotation Logic --
	# 根据冲击方向所在的象限决定旋转方向，模拟更真实的扭矩效果。
	var rotation_direction = sign(-dir.x * dir.y)

	# 处理冲击方向恰好在坐标轴上的情况。
	if rotation_direction == 0:
		if dir.x != 0: # 水平冲击
			rotation_direction = - sign(dir.x)
		else: # 垂直冲击
			rotation_direction = sign(randf_range(-1.0, 1.0)) # 随机旋转

	rotation_amount *= rotation_direction
	# -- End of New Rotation Logic --

	# Tween sequence for box
	var box_up_duration: float = 0.1
	var box_down_duration: float = 0.3

	var target_rotation = box_original_rot + rotation_amount
	var target_up_pos_no_pivot = box_original_pos - Vector2(0, bounce_height)

	# 3. Calculate the position adjustment needed to simulate rotation around the visual center.
	var pivot_correction = visual_center_offset - visual_center_offset.rotated(rotation_amount)
	var target_up_pos_with_pivot = target_up_pos_no_pivot + pivot_correction

	var prop_tween_pos = box_tween.tween_property(box_shape, "position", target_up_pos_with_pivot, box_up_duration)
	prop_tween_pos.set_ease(Tween.EASE_OUT).set_trans(Tween.TRANS_SINE)

	var prop_tween_rot = box_tween.parallel().tween_property(box_shape, "rotation", target_rotation, box_up_duration)
	prop_tween_rot.set_ease(Tween.EASE_OUT).set_trans(Tween.TRANS_SINE)

	# Fall back - no correction needed as it returns to original rotation
	var fall_tween_pos = box_tween.tween_property(box_shape, "position", box_original_pos, box_down_duration)
	fall_tween_pos.set_ease(Tween.EASE_OUT).set_trans(Tween.TRANS_BOUNCE)

	var fall_tween_rot = box_tween.parallel().tween_property(box_shape, "rotation", box_original_rot, box_down_duration)
	fall_tween_rot.set_ease(Tween.EASE_OUT).set_trans(Tween.TRANS_BOUNCE)

	# 2. Enemies jump animation
	for enemy in enemy_array:
		if not is_instance_valid(enemy):
			continue

		var enemy_tween = create_tween()
		_shake_tweens.append(enemy_tween)
		enemy_tween.finished.connect(_on_shake_tween_finished)
		_active_shake_tweens_count += 1

		var enemy_original_pos := enemy.position
		var jump_height := randf_range(15.0, 35.0) * intensity
		var jump_up_duration := 0.12
		var jump_down_duration := 0.25

		# Jump up
		enemy_tween.tween_property(enemy, "position", enemy_original_pos - Vector2(0, jump_height), jump_up_duration).set_ease(Tween.EASE_OUT).set_trans(Tween.TRANS_QUAD)
		# Fall down
		enemy_tween.tween_property(enemy, "position", enemy_original_pos, jump_down_duration).set_ease(Tween.EASE_OUT).set_trans(Tween.TRANS_BOUNCE)

	if _active_shake_tweens_count == 0:
		shake_finished.emit()


# 使用新配置系统放置敌人单元
func _place_random_enemies_with_config(grid_config: Array, enemy_config: EnemySpawnConfig) -> void:
	# 计算格子大小
	var cell_size: Vector2 = box_shape.cell_size
	# 计算格子的起始位置（相对于box_shape的中心位置）
	var grid_origin = - Vector2(cell_size.x * 3, cell_size.y * 3) / 2.0

	# 遍历每一行
	for row in range(3):
		var row_config = grid_config[row]
		# 检查每一列
		for col in range(3):
			var bit_value = int(pow(2, 2 - col)) # 左(4)、中(2)、右(1)
			# 如果该格子被选中
			if row_config & bit_value:
				# 根据权重随机选择一个单元配置
				var unit_config: EnemyUnitConfig = enemy_config.get_random_unit_config()
				if unit_config and unit_config.unit_scene:
					# 实例化敌人单元场景
					var enemy_instance: EnemyUnit = unit_config.unit_scene.instantiate()
					if enemy_instance:
						# 计算格子中心位置
						var cell_position = grid_origin + Vector2(cell_size.x * (col + 0.5), cell_size.y * (row + 0.5))
						# 设置位置
						enemy_instance.position = cell_position

						# 添加为子节点
						add_child(enemy_instance)

						# 随机选择一个动画帧
						if enemy_instance.sprite_frames:
							enemy_instance.frame = randi_range(0, enemy_instance.sprite_frames.get_frame_count(enemy_instance.animation) - 1)

						# 调整敌人缩放以适应格子大小
						if auto_scale_to_cell:
							_adjust_enemy_scale(enemy_instance, cell_size)

						enemy_array.append(enemy_instance)

						# 如果在编辑器中，设置为不可编辑
						if Engine.is_editor_hint():
							enemy_instance.set_owner(get_tree().edited_scene_root)

	_create_or_update_mask()

	# 敌人被重新挂载到遮罩节点后，记录其初始位置
	_enemies_initial_pos.clear()
	for enemy in enemy_array:
		if is_instance_valid(enemy):
			_enemies_initial_pos[enemy] = enemy.position
