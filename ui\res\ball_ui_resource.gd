class_name BallUIResource
extends Resource

## 弹球UI资源类
##
## 用于配置弹球的UI显示信息，包括名称、描述、图标等
## 通过资源文件的方式管理，便于配置和本地化

## 弹球名称
@export var ball_name: String = "未命名弹球"

## 弹球描述（支持BBCode富文本和动态占位符）
@export_multiline var description: String = "这是一个弹球的描述。"

## 图标类型枚举
enum IconType {
	EMOJI,          ## emoji文本图标
	TEXTURE,        ## 静态图片
	SHADER_MATERIAL ## 着色器材质（游戏中的实际外观）
}

## 图标类型
@export var icon_type: IconType = IconType.EMOJI

## 弹球图标（emoji或符号）
@export var icon_text: String = "⚽"

## 图片图标
@export var icon_texture: Texture2D

## 着色器材质图标
@export var icon_shader_material: ShaderMaterial

## 弹球稀有度
@export_enum("普通", "稀有", "史诗", "传说", "神话") var rarity: String = "普通"


## 获取稀有度对应的颜色
func get_rarity_color() -> Color:
	match rarity:
		"普通":
			return Color.WHITE
		"稀有":
			return Color.GREEN
		"史诗":
			return Color.BLUE
		"传说":
			return Color.PURPLE
		"神话":
			return Color.ORANGE
		_:
			return Color.GRAY

## 获取显示名称（包含稀有度信息）
func get_display_name() -> String:
	if rarity != "普通":
		return "%s (%s)" % [ball_name, rarity]
	else:
		return ball_name

## 获取当前图标（根据类型返回对应的图标）
func get_icon() -> Variant:
	match icon_type:
		IconType.EMOJI:
			return icon_text
		IconType.TEXTURE:
			return icon_texture
		IconType.SHADER_MATERIAL:
			return icon_shader_material
		_:
			return icon_text

## 获取图标的显示文本（用于UI显示）
func get_icon_display():
	match icon_type:
		IconType.EMOJI:
			return icon_text
		IconType.TEXTURE:
			return icon_texture
		IconType.SHADER_MATERIAL:
			return icon_shader_material
		_:
			return icon_text

## 检查资源是否有效
func is_valid() -> bool:
	if ball_name == "":
		return false

	match icon_type:
		IconType.EMOJI:
			return icon_text != ""
		IconType.TEXTURE:
			return icon_texture != null
		IconType.SHADER_MATERIAL:
			return icon_shader_material != null
		_:
			return icon_text != ""
