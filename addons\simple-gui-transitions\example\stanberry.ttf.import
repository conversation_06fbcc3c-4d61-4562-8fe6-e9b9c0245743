[remap]

importer="font_data_dynamic"
type="FontFile"
uid="uid://ct0m40v1h6h20"
path="res://.godot/imported/stanberry.ttf-6cc08733ad825ae8f2162bdc4983de48.fontdata"

[deps]

source_file="res://addons/simple-gui-transitions/example/stanberry.ttf"
dest_files=["res://.godot/imported/stanberry.ttf-6cc08733ad825ae8f2162bdc4983de48.fontdata"]

[params]

Rendering=null
antialiasing=1
generate_mipmaps=false
disable_embedded_bitmaps=true
multichannel_signed_distance_field=false
msdf_pixel_range=8
msdf_size=48
allow_system_fallback=true
force_autohinter=false
hinting=1
subpixel_positioning=1
keep_rounding_remainders=true
oversampling=0.0
Fallbacks=null
fallbacks=[]
Compress=null
compress=true
preload=[{
"chars": [],
"glyphs": [],
"name": "New Configuration",
"size": Vector2i(16, 0)
}]
language_support={}
script_support={}
opentype_features={}
