[gd_scene load_steps=12 format=3 uid="uid://di62j1wbs7p20"]

[ext_resource type="Script" uid="uid://byooou6785n7k" path="res://test/test.gd" id="1_ppyta"]
[ext_resource type="PackedScene" uid="uid://lo3iw104cq13" path="res://enemy/prefab/enemy.tscn" id="2_8uh7m"]
[ext_resource type="PackedScene" uid="uid://b1gii88kkua5h" path="res://ball/prefab/shock_ball.tscn" id="3_hrq6i"]
[ext_resource type="PackedScene" uid="uid://c8xb1318glvbm" path="res://bg/rock_bg.tscn" id="4_tbfq4"]
[ext_resource type="PackedScene" uid="uid://cqjbmotilkpv8" path="res://enemy/prefab/enemy_vis.tscn" id="5_s3n3v"]
[ext_resource type="PackedScene" uid="uid://b5yhb4ta5ddfx" path="res://enemy/prefab/level1/level_1_enemy_1.tscn" id="6_s3n3v"]
[ext_resource type="PackedScene" uid="uid://bw8xk7y2n8qfj" path="res://enemy/prefab/bullet/fire_bullet.tscn" id="7_kgdm3"]
[ext_resource type="Shader" uid="uid://bionbfobjqg6w" path="res://assets/materials/shader/radiation_ball.gdshader" id="8_aeque"]

[sub_resource type="Environment" id="Environment_kgdm3"]
background_mode = 3
glow_enabled = true
glow_intensity = 1.5
glow_hdr_scale = 0.5

[sub_resource type="CanvasItemMaterial" id="CanvasItemMaterial_s3n3v"]
light_mode = 1

[sub_resource type="ShaderMaterial" id="ShaderMaterial_f6u32"]
shader = ExtResource("8_aeque")
shader_parameter/time_scale = 3.295
shader_parameter/time_offset = 0.0
shader_parameter/core_color = Color(0.761671, 0.665957, 0, 1)
shader_parameter/edge_color = Color(0.736296, 0.216338, 0, 1)
shader_parameter/size_scale = 1.4
shader_parameter/edge_softness = 0.0
shader_parameter/core_size = 2.0
shader_parameter/edge_size = 0.0
shader_parameter/noise_scale = 2.5
shader_parameter/noise_strength = 0.825

[node name="Test" type="Node2D"]
script = ExtResource("1_ppyta")
enemy_scene = ExtResource("2_8uh7m")

[node name="CanvasModulate" type="CanvasModulate" parent="."]

[node name="Button" type="Button" parent="."]
offset_left = 45.0
offset_top = 43.0
offset_right = 138.0
offset_bottom = 92.0
theme_override_font_sizes/font_size = 32
text = "死亡"

[node name="WorldEnvironment" type="WorldEnvironment" parent="."]
environment = SubResource("Environment_kgdm3")

[node name="ShockBall" parent="." instance=ExtResource("3_hrq6i")]
position = Vector2(342, 576)

[node name="BGNormal1" parent="." instance=ExtResource("4_tbfq4")]

[node name="Level1Enemy1" parent="." instance=ExtResource("6_s3n3v")]
material = SubResource("CanvasItemMaterial_s3n3v")
position = Vector2(231, 646)

[node name="EnemyVis" parent="." instance=ExtResource("5_s3n3v")]
position = Vector2(444, 630)

[node name="EnemyBullet" parent="." instance=ExtResource("7_kgdm3")]
position = Vector2(249, 677)

[node name="Sprite2D" parent="EnemyBullet" index="1"]
material = SubResource("ShaderMaterial_f6u32")

[editable path="BGNormal1"]
[editable path="EnemyBullet"]
