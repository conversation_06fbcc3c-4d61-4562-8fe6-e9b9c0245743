extends Control
class_name Bar

@onready var top_bar: TextureProgressBar = $Top
@onready var bottom_bar: ProgressBar = $Bottom


var value = 0.0 :set = _set_value


func _set_value(new_value: float):
	value = new_value
	create_tween().tween_property(top_bar, "value", new_value, 0.2).set_trans(Tween.TRANS_SINE).set_ease(Tween.EASE_IN)
	create_tween().tween_property(top_bar.material, "shader_parameter/EdgeWaveLevel", new_value, 0.2).set_trans(Tween.TRANS_SINE).set_ease(Tween.EASE_IN)
