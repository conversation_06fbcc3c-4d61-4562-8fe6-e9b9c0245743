extends Node
class_name UpgradeSystemIntegrationExample

## 升级系统集成示例
##
## 展示如何在实际项目中使用升级选项生成器
## 包括初始化、配置管理和动态调整的完整流程

## 升级选项生成器实例
var upgrade_generator: UpgradeOptionGenerator

## 可用的弹球和遗物资源路径
const BALL_RESOURCES = [
	"res://ball/prefab/poison_ball.tscn",
	"res://ball/prefab/shock_ball.tscn",
	"res://ball/prefab/normal_ball.tscn"
]

const RELIC_RESOURCES = [
	"res://relic/relics/iron_armor.tres",
	"res://relic/relics/burn_oil.tres"
]

func _ready() -> void:
	# 初始化升级选项生成器
	initialize_upgrade_generator()
	
	# 加载并配置所有可用物品
	load_and_configure_items()
	
	# 根据游戏进度调整权重
	adjust_weights_by_game_progress()
	
	# 打印当前配置状态
	print_current_configuration()

## 初始化升级选项生成器
func initialize_upgrade_generator() -> void:
	upgrade_generator = UpgradeOptionGenerator.new()
	print("升级选项生成器已初始化")

## 加载并配置所有可用物品
func load_and_configure_items() -> void:
	print("\n=== 加载并配置物品 ===")
	
	# 加载弹球资源
	load_ball_resources()
	
	# 加载遗物资源
	load_relic_resources()
	
	# 手动添加一些特殊配置
	add_special_configurations()

## 加载弹球资源
func load_ball_resources() -> void:
	print("加载弹球资源...")
	
	for ball_path in BALL_RESOURCES:
		var ball_scene = load(ball_path) as PackedScene
		if ball_scene:
			var ball_instance = ball_scene.instantiate() as BallBase
			if ball_instance and ball_instance.ui_resource:
				# 根据弹球类型设置不同的默认权重
				var default_weight = get_default_ball_weight(ball_instance)
				upgrade_generator.add_ball_weight_config(ball_instance, default_weight)
				print("  已添加弹球: %s (权重: %.1f)" % [ball_instance.ui_resource.ball_name, default_weight])
			
			# 清理实例
			if ball_instance:
				ball_instance.queue_free()

## 加载遗物资源
func load_relic_resources() -> void:
	print("加载遗物资源...")
	
	for relic_path in RELIC_RESOURCES:
		var relic_resource = load(relic_path) as Relic
		if relic_resource:
			# 根据遗物效果设置不同的默认权重
			var default_weight = get_default_relic_weight(relic_resource)
			upgrade_generator.add_relic_weight_config(relic_resource, default_weight)
			print("  已添加遗物: %s (权重: %.1f)" % [relic_resource.name, default_weight])

## 添加特殊配置
func add_special_configurations() -> void:
	print("添加特殊配置...")
	
	# 添加一些虚拟的高稀有度物品用于演示
	upgrade_generator.set_item_weight_config(
		"legendary_dragon_ball", 
		UpgradeOptionGenerator.RarityLevel.LEGENDARY, 
		0.5, 
		"ball"
	)
	
	upgrade_generator.set_item_weight_config(
		"epic_ancient_relic", 
		UpgradeOptionGenerator.RarityLevel.EPIC, 
		2.0, 
		"relic"
	)
	
	print("  已添加特殊配置物品")

## 根据弹球类型获取默认权重
func get_default_ball_weight(ball: BallBase) -> float:
	if not ball.ui_resource:
		return 5.0
	
	# 根据稀有度设置基础权重
	var rarity = UpgradeOptionGenerator.extract_ball_rarity(ball.ui_resource)
	match rarity:
		UpgradeOptionGenerator.RarityLevel.COMMON:
			return 15.0
		UpgradeOptionGenerator.RarityLevel.RARE:
			return 8.0
		UpgradeOptionGenerator.RarityLevel.EPIC:
			return 3.0
		UpgradeOptionGenerator.RarityLevel.LEGENDARY:
			return 1.0
		_:
			return 5.0

## 根据遗物类型获取默认权重
func get_default_relic_weight(relic: Relic) -> float:
	# 简单的权重分配策略
	# 实际项目中可以根据遗物效果的强度来设置
	return 6.0

## 根据游戏进度调整权重
func adjust_weights_by_game_progress() -> void:
	print("\n=== 根据游戏进度调整权重 ===")
	
	# 模拟游戏进度（实际项目中从GameManager获取）
	var player_level = get_simulated_player_level()
	var game_stage = get_simulated_game_stage()
	
	print("当前玩家等级: %d, 游戏阶段: %s" % [player_level, game_stage])
	
	# 根据玩家等级调整稀有度权重
	adjust_weights_by_player_level(player_level)
	
	# 根据游戏阶段调整权重
	adjust_weights_by_game_stage(game_stage)

## 根据玩家等级调整权重
func adjust_weights_by_player_level(level: int) -> void:
	if level >= 10:
		# 高等级玩家，提高稀有物品出现率
		upgrade_generator.adjust_rarity_weights_multiply(UpgradeOptionGenerator.RarityLevel.RARE, 1.5)
		print("  玩家等级较高，稀有物品权重增加50%")
	
	if level >= 20:
		# 更高等级，提高史诗物品出现率
		upgrade_generator.adjust_rarity_weights_multiply(UpgradeOptionGenerator.RarityLevel.EPIC, 2.0)
		print("  玩家等级很高，史诗物品权重增加100%")
	
	if level >= 30:
		# 最高等级，提高传说物品出现率
		upgrade_generator.adjust_rarity_weights_multiply(UpgradeOptionGenerator.RarityLevel.LEGENDARY, 3.0)
		print("  玩家等级极高，传说物品权重增加200%")

## 根据游戏阶段调整权重
func adjust_weights_by_game_stage(stage: String) -> void:
	match stage:
		"early":
			# 早期游戏，降低高稀有度物品权重
			upgrade_generator.adjust_rarity_weights_multiply(UpgradeOptionGenerator.RarityLevel.EPIC, 0.5)
			upgrade_generator.adjust_rarity_weights_multiply(UpgradeOptionGenerator.RarityLevel.LEGENDARY, 0.2)
			print("  早期游戏阶段，降低高稀有度物品权重")
		
		"mid":
			# 中期游戏，平衡调整
			upgrade_generator.adjust_rarity_weights_multiply(UpgradeOptionGenerator.RarityLevel.RARE, 1.2)
			print("  中期游戏阶段，稍微提高稀有物品权重")
		
		"late":
			# 后期游戏，大幅提高高稀有度物品权重
			upgrade_generator.adjust_rarity_weights_multiply(UpgradeOptionGenerator.RarityLevel.EPIC, 2.5)
			upgrade_generator.adjust_rarity_weights_multiply(UpgradeOptionGenerator.RarityLevel.LEGENDARY, 5.0)
			print("  后期游戏阶段，大幅提高高稀有度物品权重")

## 打印当前配置状态
func print_current_configuration() -> void:
	print("\n=== 当前配置状态 ===")
	upgrade_generator.print_weight_stats()
	
	# 显示一些具体的配置示例
	print("\n具体配置示例:")
	var all_configs = upgrade_generator.get_all_weight_configs()
	for i in range(min(5, all_configs.size())):
		var config = all_configs[i]
		print("  %s: %s稀有度, 权重%.2f, 类型%s" % [
			config.item_id,
			UpgradeOptionGenerator.get_rarity_display_name(config.rarity),
			config.weight,
			config.item_type
		])

## 模拟获取玩家等级
func get_simulated_player_level() -> int:
	# 实际项目中应该从GameManager获取
	return 15

## 模拟获取游戏阶段
func get_simulated_game_stage() -> String:
	# 实际项目中应该根据游戏进度判断
	return "mid"

## 生成升级选项（示例方法）
func generate_upgrade_options(count: int = 3) -> Array[Dictionary]:
	print("\n=== 生成升级选项 ===")
	
	var options: Array[Dictionary] = []
	var all_configs = upgrade_generator.get_all_weight_configs()
	
	if all_configs.is_empty():
		print("没有可用的物品配置")
		return options
	
	# 计算总权重
	var total_weight: float = 0.0
	for config in all_configs:
		total_weight += config.weight
	
	print("总权重: %.2f" % total_weight)
	
	# 生成指定数量的选项
	for i in range(count):
		var selected_config = select_weighted_random_config(all_configs, total_weight)
		if selected_config:
			var option = {
				"id": selected_config.item_id,
				"rarity": selected_config.rarity,
				"type": selected_config.item_type,
				"weight": selected_config.weight
			}
			options.append(option)
			print("  选项 %d: %s (%s稀有度, 权重%.2f)" % [
				i + 1,
				selected_config.item_id,
				UpgradeOptionGenerator.get_rarity_display_name(selected_config.rarity),
				selected_config.weight
			])
	
	return options

## 按权重随机选择配置
func select_weighted_random_config(configs: Array[UpgradeOptionGenerator.ItemWeightConfig], total_weight: float) -> UpgradeOptionGenerator.ItemWeightConfig:
	if configs.is_empty() or total_weight <= 0.0:
		return null
	
	var random_value = randf() * total_weight
	var cumulative_weight: float = 0.0
	
	for config in configs:
		cumulative_weight += config.weight
		if random_value <= cumulative_weight:
			return config
	
	# 如果没有选中任何配置，返回最后一个
	return configs[-1]

## 公共接口：获取升级选项生成器
func get_upgrade_generator() -> UpgradeOptionGenerator:
	return upgrade_generator

## 公共接口：重新配置权重
func reconfigure_weights() -> void:
	print("\n=== 重新配置权重 ===")
	upgrade_generator.clear_all_weight_configs()
	load_and_configure_items()
	adjust_weights_by_game_progress()
	print("权重配置已重新加载")
