@tool
class_name BallDamageAttribute
extends Attribute

@export var min_value_source: String
@export var max_value_source: String
@export var crit_rate_source: String

var _last_crit: bool = false


func pop_last_crit() -> bool:
	var v: bool = _last_crit
	_last_crit = false
	return v


func get_value(consume_buffs: bool = false, consume_buffs_dependencies: bool = false) -> float:
	return super.get_value(consume_buffs, consume_buffs_dependencies)


func post_attribute_value_changed(_value: float) -> float:
	return _value


func custom_compute(operated_value: float, _compute_params: Array[Attribute]) -> float:
	var min_damage: float = _compute_params[0].get_value()
	var max_damage: float = _compute_params[1].get_value()
	var crit_rate: float = _compute_params[2].get_value()
	var damage = base_value + randf_range(min_damage, max_damage)
	var is_crit = randf() < crit_rate / 100.0
	_last_crit = is_crit
	if is_crit:
		damage *= 2.0 # crit multiplier
	return damage


## 属性依赖列表
## @ return: 返回依赖属性的名称数组
func derived_from() -> Array[String]:
	return [
	min_value_source,
	max_value_source,
	crit_rate_source
	]
