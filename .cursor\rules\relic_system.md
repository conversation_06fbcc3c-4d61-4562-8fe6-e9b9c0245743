---
description: 遗物系统实现原理
globs: 
alwaysApply: false
---
---
# 遗物系统实现原理

## 整体架构
- 采用事件驱动的组件化设计，将遗物数据与效果逻辑解耦
- **Relic**: `Resource` 类型，定义遗物的基础数据（名称、描述、图标）和效果（`RelicEffect`）
- **RelicEffect**: `Resource` 类型，所有遗物效果的基类，定义了 `on_acquired` 和 `on_lost` 接口
- **RelicManager**: 负责管理玩家持有的所有遗物，在添加或移除遗物时调用其效果的生命周期方法
- **GameEvents**: 全局事件总线（单例），用于广播游戏内关键事件，如弹球击中敌人、敌人死亡等，是触发被动遗物效果的核心

## 效果实现机制
- **被动效果**:
  - `PassiveStatRelicEffect`：直接在获取时向玩家的属性组件（`AttributeComponent`）添加一个永久性的 `AttributeBuff`，实现属性的持续加成或修改。失去遗物时则移除该 `Buff`。
  - 案例：`iron_armor.tres`（能量护盾）通过此机制为玩家添加一个永久的“伤害减免”`Buff`。
- **条件触发效果**:
  - `ConditionalDamageRelicEffect` / `PosConditionRelicEffect`：在获取时订阅 `GameEvents` 中的特定事件（如 `ball_hit_enemy`）。
  - 当事件触发时，检查是否满足预设条件（如敌人是否有特定Buff、弹球是否在特定区域）。
  - 条件满足时，动态地向相关对象（如弹球）添加一个**有时效的、一次性的** `AttributeBuff`，以实现临时的效果（如“下次攻击伤害增加”）。
  - 案例：`burn_oil.tres`（助燃剂）监听 `ball_hit_enemy` 事件，如果敌人带有“burn” `Buff`，就给弹球添加一个临时的伤害加成 `Buff`。

## 扩展性设计
- **数据驱动**: 新增遗物只需创建新的 `Relic` 资源文件，并为其配置相应的 `RelicEffect` 资源，无需修改核心代码。
- **效果组合**: 可以通过创建新的 `RelicEffect` 子类来实现复杂多样的遗物效果。
- **事件解耦**: 依赖 `GameEvents` 的设计使得遗物系统可以响应任何游戏模块广播的事件，具有良好的可扩展性。


