shader_type canvas_item;
render_mode unshaded;

#include "res://addons/shader_utils/canvas_group_blend.gdshaderinc"

uniform sampler2D	noise_tex			: hint_default_white, repeat_enable;
uniform sampler2D	gradient_tex		: hint_default_black;
uniform float		poster_color		: hint_range(0.0, 16.0) = 6.0;
uniform float		effect_alpha			: hint_range(0.0, 1.0) = 1.0;
uniform bool		show_base_texture	= true;
uniform vec2		effect_speed			= vec2(0.015, 0.5);
uniform float		effect_aperture		: hint_range(0.0, 5.0) = 0.22;
uniform float		vignette_radius		: hint_range(0.0, 1.0) = 0.25; //fade start
uniform float		vignette_falloff	: hint_range(0.0, 0.5) = 0.25; //fade width
uniform float		noise_influence		: hint_range(0.0, 1.0) = 1.00; //noise influence

vec2 polar_coordinates(vec2 uv, vec2 center, float zoom, float repeat) {
    vec2 d = uv - center;
    float r     = length(d) * 2.0;
    float theta = atan(d.y, d.x) * (1.0 / (2.0 * 3.1416));
    return mod(vec2(r * zoom, theta * repeat), 1.0);
}

void fragment() {
	vec4 base_color = texture(TEXTURE, UV);
	float shape_alpha = base_color.a;
	vec2  center = vec2(0.5);
	vec2  p = polar_coordinates(UV, center, 1.0, 1.0);

	p.x += TIME * effect_speed.y;
	p.y += sin(TIME) * effect_speed.x; //-> X is rotation!

	float n = texture(noise_tex, p).r;

	float dist = distance(UV, center);
	float edge = clamp(1.0 - dist, 0.0, 1.0);
	float noise_val = edge * (((edge + effect_aperture) * n - effect_aperture) * 75.0);
	noise_val = clamp(noise_val, 0.0, 1.0);

	float effective_radius = vignette_radius + n * noise_influence * vignette_falloff;
	float mask = smoothstep(effective_radius + vignette_falloff, effective_radius, 1.0-dist);

	float alpha = noise_val * effect_alpha * mask * shape_alpha;

	//color posterization
	vec4 effect_color;
	if (poster_color >= 1.0){
		float quantized = floor(n * poster_color) / poster_color;
		effect_color = texture(gradient_tex, vec2(quantized, 0.5));
		alpha = floor(alpha * poster_color) / poster_color;
	}
	else{
		effect_color = texture(gradient_tex, vec2(n, 0.5));
	}
	effect_color.a = alpha;

	// vec4 final_color;
	// if (show_base_texture) {
	// 	// Mix the base color and the effect color, using the effect's alpha as the blend amount.
	// 	// The final alpha is the base texture's alpha to preserve the original shape.
	// 	final_color = vec4(base_color.rgb + effect_color.rgb * alpha, base_color.a);
	// } else {
	// 	final_color = vec4(effect_color.rgb, alpha);
	// 	final_color.a *= base_color.a;
	// }

	COLOR = blend_canvas_background(effect_color, SCREEN_UV);
}