class_name UpgradeOptionGenerator
extends RefCounted

## 升级选项生成器
##
## 负责生成升级选项，包括稀有度系统、权重配置和批量调整功能
## 支持弹球和遗物的统一稀有度管理

## 稀有度等级枚举
enum RarityLevel {
	COMMON,    ## 普通
	RARE,      ## 稀有  
	EPIC,      ## 史诗
	LEGENDARY  ## 传说
}

## 稀有度等级字典（英文标识符 -> 中文显示名称）
const RARITY_NAMES: Dictionary = {
	RarityLevel.COMMON: "普通",
	RarityLevel.RARE: "稀有", 
	RarityLevel.EPIC: "史诗",
	RarityLevel.LEGENDARY: "传说"
}

## 稀有度等级字典（中文显示名称 -> 英文标识符）
const RARITY_KEYS: Dictionary = {
	"普通": RarityLevel.COMMON,
	"稀有": RarityLevel.RARE,
	"史诗": RarityLevel.EPIC, 
	"传说": RarityLevel.LEGENDARY
}

## 稀有度等级字典（英文字符串 -> 枚举值）
const RARITY_STRING_TO_ENUM: Dictionary = {
	"common": RarityLevel.COMMON,
	"rare": RarityLevel.RARE,
	"epic": RarityLevel.EPIC,
	"legendary": RarityLevel.LEGENDARY
}

## 稀有度等级字典（枚举值 -> 英文字符串）
const RARITY_ENUM_TO_STRING: Dictionary = {
	RarityLevel.COMMON: "common",
	RarityLevel.RARE: "rare", 
	RarityLevel.EPIC: "epic",
	RarityLevel.LEGENDARY: "legendary"
}

## 物品权重配置结构
class ItemWeightConfig:
	var item_id: String = ""           ## 物品唯一标识符
	var rarity: RarityLevel = RarityLevel.COMMON  ## 稀有度等级
	var weight: float = 1.0            ## 权重值（必须>=0）
	var item_type: String = ""         ## 物品类型（"ball" 或 "relic"）
	var item_resource = null           ## 物品资源引用
	
	func _init(p_item_id: String = "", p_rarity: RarityLevel = RarityLevel.COMMON, p_weight: float = 1.0, p_item_type: String = ""):
		item_id = p_item_id
		rarity = p_rarity
		weight = max(0.0, p_weight)  # 确保权重>=0
		item_type = p_item_type
	
	## 验证配置是否有效
	func is_valid() -> bool:
		return item_id != "" and weight >= 0.0 and item_type != ""

## 权重配置存储
var _weight_configs: Dictionary = {}  # item_id -> ItemWeightConfig

## 获取稀有度的中文显示名称
## @param rarity: 稀有度等级枚举值
## @return: 中文显示名称
static func get_rarity_display_name(rarity: RarityLevel) -> String:
	return RARITY_NAMES.get(rarity, "未知")

## 获取稀有度的英文标识符
## @param rarity: 稀有度等级枚举值  
## @return: 英文标识符字符串
static func get_rarity_key(rarity: RarityLevel) -> String:
	return RARITY_ENUM_TO_STRING.get(rarity, "common")

## 从中文名称获取稀有度等级
## @param display_name: 中文显示名称
## @return: 稀有度等级枚举值
static func get_rarity_from_display_name(display_name: String) -> RarityLevel:
	return RARITY_KEYS.get(display_name, RarityLevel.COMMON)

## 从英文字符串获取稀有度等级
## @param key: 英文标识符字符串
## @return: 稀有度等级枚举值
static func get_rarity_from_key(key: String) -> RarityLevel:
	return RARITY_STRING_TO_ENUM.get(key, RarityLevel.COMMON)

## 获取稀有度对应的颜色
## @param rarity: 稀有度等级枚举值
## @return: 对应的颜色
static func get_rarity_color(rarity: RarityLevel) -> Color:
	match rarity:
		RarityLevel.COMMON:
			return Color.WHITE
		RarityLevel.RARE:
			return Color.GREEN
		RarityLevel.EPIC:
			return Color.BLUE
		RarityLevel.LEGENDARY:
			return Color.PURPLE
		_:
			return Color.GRAY

## 添加或更新物品权重配置
## @param item_id: 物品唯一标识符
## @param rarity: 稀有度等级
## @param weight: 权重值（必须>=0）
## @param item_type: 物品类型（"ball" 或 "relic"）
## @param item_resource: 物品资源引用（可选）
func set_item_weight_config(item_id: String, rarity: RarityLevel, weight: float, item_type: String, item_resource = null) -> void:
	if item_id == "":
		push_error("UpgradeOptionGenerator: 物品ID不能为空")
		return
	
	if weight < 0.0:
		push_warning("UpgradeOptionGenerator: 权重值不能小于0，已自动调整为0")
		weight = 0.0
	
	var config = ItemWeightConfig.new(item_id, rarity, weight, item_type)
	config.item_resource = item_resource
	_weight_configs[item_id] = config
	
	print("UpgradeOptionGenerator: 设置物品权重配置 - ID: %s, 稀有度: %s, 权重: %.2f, 类型: %s" % [item_id, get_rarity_display_name(rarity), weight, item_type])

## 获取物品权重配置
## @param item_id: 物品唯一标识符
## @return: ItemWeightConfig实例，如果不存在返回null
func get_item_weight_config(item_id: String) -> ItemWeightConfig:
	return _weight_configs.get(item_id, null)

## 获取所有权重配置
## @return: 所有ItemWeightConfig实例的数组
func get_all_weight_configs() -> Array[ItemWeightConfig]:
	var configs: Array[ItemWeightConfig] = []
	for config in _weight_configs.values():
		configs.append(config)
	return configs

## 按稀有度筛选物品配置
## @param rarity: 稀有度等级
## @return: 指定稀有度的ItemWeightConfig数组
func get_configs_by_rarity(rarity: RarityLevel) -> Array[ItemWeightConfig]:
	var filtered_configs: Array[ItemWeightConfig] = []
	for config in _weight_configs.values():
		if config.rarity == rarity:
			filtered_configs.append(config)
	return filtered_configs

## 按物品类型筛选物品配置
## @param item_type: 物品类型（"ball" 或 "relic"）
## @return: 指定类型的ItemWeightConfig数组
func get_configs_by_type(item_type: String) -> Array[ItemWeightConfig]:
	var filtered_configs: Array[ItemWeightConfig] = []
	for config in _weight_configs.values():
		if config.item_type == item_type:
			filtered_configs.append(config)
	return filtered_configs

## 批量调整指定稀有度的物品权重（加法）
## @param rarity: 稀有度等级
## @param add_value: 要增加的权重值
## @return: 受影响的物品数量
func adjust_rarity_weights_add(rarity: RarityLevel, add_value: float) -> int:
	var affected_count: int = 0
	var configs = get_configs_by_rarity(rarity)
	
	for config in configs:
		var new_weight = max(0.0, config.weight + add_value)  # 确保权重>=0
		config.weight = new_weight
		affected_count += 1
	
	print("UpgradeOptionGenerator: 批量调整稀有度权重（加法） - 稀有度: %s, 增加值: %.2f, 影响物品数: %d" % [get_rarity_display_name(rarity), add_value, affected_count])
	return affected_count

## 批量调整指定稀有度的物品权重（乘法）
## @param rarity: 稀有度等级
## @param multiplier: 权重倍数（必须>=0）
## @return: 受影响的物品数量
func adjust_rarity_weights_multiply(rarity: RarityLevel, multiplier: float) -> int:
	if multiplier < 0.0:
		push_warning("UpgradeOptionGenerator: 权重倍数不能小于0，已自动调整为0")
		multiplier = 0.0
	
	var affected_count: int = 0
	var configs = get_configs_by_rarity(rarity)
	
	for config in configs:
		config.weight = config.weight * multiplier
		affected_count += 1
	
	print("UpgradeOptionGenerator: 批量调整稀有度权重（乘法） - 稀有度: %s, 倍数: %.2f, 影响物品数: %d" % [get_rarity_display_name(rarity), multiplier, affected_count])
	return affected_count

## 批量设置指定稀有度的物品权重为固定值
## @param rarity: 稀有度等级
## @param new_weight: 新的权重值（必须>=0）
## @return: 受影响的物品数量
func set_rarity_weights(rarity: RarityLevel, new_weight: float) -> int:
	if new_weight < 0.0:
		push_warning("UpgradeOptionGenerator: 权重值不能小于0，已自动调整为0")
		new_weight = 0.0
	
	var affected_count: int = 0
	var configs = get_configs_by_rarity(rarity)
	
	for config in configs:
		config.weight = new_weight
		affected_count += 1
	
	print("UpgradeOptionGenerator: 批量设置稀有度权重 - 稀有度: %s, 新权重: %.2f, 影响物品数: %d" % [get_rarity_display_name(rarity), new_weight, affected_count])
	return affected_count

## 移除物品权重配置
## @param item_id: 物品唯一标识符
## @return: 是否成功移除
func remove_item_weight_config(item_id: String) -> bool:
	if _weight_configs.has(item_id):
		_weight_configs.erase(item_id)
		print("UpgradeOptionGenerator: 移除物品权重配置 - ID: %s" % item_id)
		return true
	return false

## 清空所有权重配置
func clear_all_weight_configs() -> void:
	var count = _weight_configs.size()
	_weight_configs.clear()
	print("UpgradeOptionGenerator: 清空所有权重配置，共移除 %d 个配置" % count)

## 获取权重配置统计信息
## @return: 包含统计信息的字典
func get_weight_config_stats() -> Dictionary:
	var stats = {
		"total_count": _weight_configs.size(),
		"rarity_counts": {},
		"type_counts": {},
		"total_weight": 0.0
	}
	
	# 初始化稀有度计数
	for rarity in RarityLevel.values():
		stats.rarity_counts[rarity] = 0
	
	# 统计各项数据
	for config in _weight_configs.values():
		stats.rarity_counts[config.rarity] += 1
		stats.type_counts[config.item_type] = stats.type_counts.get(config.item_type, 0) + 1
		stats.total_weight += config.weight
	
	return stats

## 验证所有权重配置的有效性
## @return: 无效配置的item_id数组
func validate_all_configs() -> Array[String]:
	var invalid_ids: Array[String] = []

	for item_id in _weight_configs.keys():
		var config = _weight_configs[item_id]
		if not config.is_valid():
			invalid_ids.append(item_id)

	if invalid_ids.size() > 0:
		push_warning("UpgradeOptionGenerator: 发现 %d 个无效的权重配置" % invalid_ids.size())

	return invalid_ids

## 从弹球资源自动提取稀有度信息
## @param ball_resource: BallUIResource实例
## @return: 对应的稀有度等级
static func extract_ball_rarity(ball_resource: BallUIResource) -> RarityLevel:
	if not is_instance_valid(ball_resource):
		return RarityLevel.COMMON

	# BallUIResource使用中文稀有度名称
	return get_rarity_from_display_name(ball_resource.rarity)

## 从遗物资源自动提取稀有度信息
## @param relic_ui_data: RelicUIData实例
## @return: 对应的稀有度等级
static func extract_relic_rarity(relic_ui_data: RelicUIData) -> RarityLevel:
	if not is_instance_valid(relic_ui_data):
		return RarityLevel.COMMON

	# RelicUIData使用英文稀有度标识符
	return get_rarity_from_key(relic_ui_data.rarity)

## 从弹球实例自动添加权重配置
## @param ball: BallBase实例
## @param default_weight: 默认权重值
## @param item_id: 自定义物品ID（可选，默认使用弹球名称）
func add_ball_weight_config(ball: BallBase, default_weight: float = 1.0, item_id: String = "") -> void:
	if not is_instance_valid(ball) or not is_instance_valid(ball.ui_resource):
		push_error("UpgradeOptionGenerator: 无效的弹球实例或缺少UI资源")
		return

	var ball_id = item_id if item_id != "" else ball.ui_resource.ball_name
	var rarity = extract_ball_rarity(ball.ui_resource)

	set_item_weight_config(ball_id, rarity, default_weight, "ball", ball)

## 从遗物实例自动添加权重配置
## @param relic: Relic实例
## @param default_weight: 默认权重值
## @param item_id: 自定义物品ID（可选，默认使用遗物名称）
func add_relic_weight_config(relic: Relic, default_weight: float = 1.0, item_id: String = "") -> void:
	if not is_instance_valid(relic):
		push_error("UpgradeOptionGenerator: 无效的遗物实例")
		return

	var relic_id = item_id if item_id != "" else relic.name
	# 遗物本身没有稀有度信息，需要从UI数据中获取
	var relic_ui_data = UIDataConverter.relic_to_ui_data(relic)
	var rarity = extract_relic_rarity(relic_ui_data) if relic_ui_data else RarityLevel.COMMON

	set_item_weight_config(relic_id, rarity, default_weight, "relic", relic)

## 批量从弹球数组添加权重配置
## @param balls: BallBase实例数组
## @param default_weight: 默认权重值
func add_balls_weight_configs(balls: Array[BallBase], default_weight: float = 1.0) -> void:
	var added_count = 0
	for ball in balls:
		if is_instance_valid(ball):
			add_ball_weight_config(ball, default_weight)
			added_count += 1

	print("UpgradeOptionGenerator: 批量添加弹球权重配置，成功添加 %d 个" % added_count)

## 批量从遗物数组添加权重配置
## @param relics: Relic实例数组
## @param default_weight: 默认权重值
func add_relics_weight_configs(relics: Array[Relic], default_weight: float = 1.0) -> void:
	var added_count = 0
	for relic in relics:
		if is_instance_valid(relic):
			add_relic_weight_config(relic, default_weight)
			added_count += 1

	print("UpgradeOptionGenerator: 批量添加遗物权重配置，成功添加 %d 个" % added_count)

## 获取稀有度权重分布信息
## @return: 包含各稀有度权重分布的字典
func get_rarity_weight_distribution() -> Dictionary:
	var distribution = {}

	# 初始化各稀有度的权重统计
	for rarity in RarityLevel.values():
		distribution[rarity] = {
			"count": 0,
			"total_weight": 0.0,
			"average_weight": 0.0,
			"items": []
		}

	# 统计各稀有度的权重信息
	for config in _weight_configs.values():
		var rarity_data = distribution[config.rarity]
		rarity_data.count += 1
		rarity_data.total_weight += config.weight
		rarity_data.items.append(config.item_id)

	# 计算平均权重
	for rarity in RarityLevel.values():
		var rarity_data = distribution[rarity]
		if rarity_data.count > 0:
			rarity_data.average_weight = rarity_data.total_weight / rarity_data.count

	return distribution

## 打印权重配置统计信息
func print_weight_stats() -> void:
	var stats = get_weight_config_stats()
	var distribution = get_rarity_weight_distribution()

	print("=== 升级选项生成器权重配置统计 ===")
	print("总物品数量: %d" % stats.total_count)
	print("总权重: %.2f" % stats.total_weight)
	print("")

	print("按稀有度分布:")
	for rarity in RarityLevel.values():
		var rarity_name = get_rarity_display_name(rarity)
		var count = stats.rarity_counts[rarity]
		var rarity_data = distribution[rarity]
		print("  %s: %d 个物品, 总权重: %.2f, 平均权重: %.2f" % [rarity_name, count, rarity_data.total_weight, rarity_data.average_weight])

	print("")
	print("按类型分布:")
	for item_type in stats.type_counts.keys():
		print("  %s: %d 个物品" % [item_type, stats.type_counts[item_type]])

	print("================================")
