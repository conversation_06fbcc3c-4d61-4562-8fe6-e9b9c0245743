extends Control

## 升级面板测试脚本
##
## 用于测试升级面板的功能，包括：
## - 显示测试数据
## - 验证交互功能
## - 检查UI布局

@onready var level_up_panel: LevelUpPanel = $LevelUpPanel

func _ready() -> void:
	# 连接面板信号
	if level_up_panel:
		level_up_panel.upgrade_selected.connect(_on_upgrade_selected)
		level_up_panel.panel_closed.connect(_on_panel_closed)

		# 延迟显示面板以确保初始化完成
		await get_tree().process_frame
		_show_test_panel()
	else:
		push_error("找不到升级面板节点")

## 显示测试面板
func _show_test_panel() -> void:
	var sample_data = _get_sample_data()
	level_up_panel.show_panel(
		sample_data.level_data,
		sample_data.balls_data,
		sample_data.relics_data,
		sample_data.options_data
	)

## 升级选择处理
func _on_upgrade_selected(upgrade_data: Dictionary) -> void:
	print("测试：升级选择完成 - ", upgrade_data.get("name", "未知选项"))

## 面板关闭处理
func _on_panel_closed() -> void:
	print("测试：升级面板已关闭")

## 获取测试数据
func _get_sample_data() -> Dictionary:
	return {
		"level_data": {
			"current_level": 5,
			"next_level": 6
		},
		"balls_data": [
			{
				"name": "基础弹球",
				"icon": "⚽",
				"description": "最基础的攻击弹球，提供稳定的伤害输出。",
				"effects": ["伤害: 10", "速度: 100", "弹跳次数: 3"]
			},
			{
				"name": "火焰弹球",
				"icon": "🔥",
				"description": "造成火焰伤害的弹球，能够点燃敌人。",
				"effects": ["伤害: 15", "附加燃烧效果", "燃烧持续时间: 3秒"]
			},
			{
				"name": "冰霜弹球",
				"icon": "❄️",
				"description": "造成冰霜伤害的弹球，能够减缓敌人移动。",
				"effects": ["伤害: 12", "附加减速效果", "减速持续时间: 2秒"]
			}
		],
		"relics_data": [
			{
				"name": "力量护符",
				"icon": "💪",
				"description": "增强攻击力的神秘护符，提升所有弹球的伤害。",
				"effects": ["攻击力 +20%", "暴击率 +5%"]
			},
			{
				"name": "速度之靴",
				"icon": "👟",
				"description": "提升移动速度的魔法靴子，让弹球飞得更快。",
				"effects": ["移动速度 +15%", "弹球速度 +10%"]
			}
		],
		"options_data": [
			{
				"name": "雷电弹球",
				"icon": "⚡",
				"description": "获得新的雷电弹球，能够在敌人间跳跃传递电击。",
				"effects": ["伤害: 18", "链式闪电攻击", "最多跳跃3个目标"],
				"type": "new_ball"
			},
			{
				"name": "火焰强化",
				"icon": "🔥+",
				"description": "升级现有的火焰弹球，大幅提升其威力和燃烧效果。",
				"effects": ["伤害: 15 → 25", "燃烧范围扩大", "燃烧伤害 +50%"],
				"type": "upgrade_ball"
			},
			{
				"name": "幸运硬币",
				"icon": "🪙",
				"description": "获得幸运硬币遗物，提升整体收益和暴击概率。",
				"effects": ["金币获得 +50%", "暴击率 +10%", "经验获得 +20%"],
				"type": "new_relic"
			}
		]
	}


## 处理输入事件（用于测试）
func _input(event: InputEvent) -> void:
	if event is InputEventKey and event.pressed:
		match event.keycode:
			KEY_T:
				_show_test_panel()
			KEY_ESCAPE:
				if level_up_panel.visible:
					level_up_panel.hide_panel()
			KEY_R:
				get_tree().reload_current_scene()
