[gd_resource type="ShaderMaterial" load_steps=6 format=3 uid="uid://cmrqqbs5mptgt"]

[ext_resource type="Shader" uid="uid://cxrkq40rs0ra" path="res://assets/materials/shader/radial_noise.gdshader" id="1_a5hot"]

[sub_resource type="Gradient" id="Gradient_y0xaf"]
offsets = PackedFloat32Array(0, 0.0857664, 1)
colors = PackedColorArray(0.139333, 0.44, 0, 1, 0, 0.19, 0.0918333, 1, 3.60981e-06, 0.806492, 0.316544, 1)

[sub_resource type="GradientTexture1D" id="GradientTexture1D_xinbo"]
gradient = SubResource("Gradient_y0xaf")

[sub_resource type="FastNoiseLite" id="FastNoiseLite_ff8xn"]
frequency = 0.0035
fractal_type = 3
fractal_gain = 0.005

[sub_resource type="NoiseTexture2D" id="NoiseTexture2D_htmum"]
seamless = true
noise = SubResource("FastNoiseLite_ff8xn")

[resource]
resource_local_to_scene = true
shader = ExtResource("1_a5hot")
shader_parameter/noise_tex = SubResource("NoiseTexture2D_htmum")
shader_parameter/gradient_tex = SubResource("GradientTexture1D_xinbo")
shader_parameter/poster_color = 6.0
shader_parameter/effect_alpha = 0.405
shader_parameter/show_base_texture = true
shader_parameter/effect_speed = Vector2(0.05, 0.2)
shader_parameter/effect_aperture = 1.429
shader_parameter/vignette_radius = 0.486
shader_parameter/vignette_falloff = 0.251
shader_parameter/noise_influence = 1.0
