[gd_scene load_steps=4 format=3 uid="uid://cdba1niudigw2"]

[ext_resource type="Script" uid="uid://bwedagipx1cfm" path="res://addons/simple-gui-transitions/example/layout_3.gd" id="1"]
[ext_resource type="Theme" uid="uid://cxbdqv0cxcy1m" path="res://addons/simple-gui-transitions/example/theme.tres" id="2"]
[ext_resource type="Script" uid="uid://bimelmc1ce1b2" path="res://addons/simple-gui-transitions/transition.gd" id="3"]

[node name="Layout3" type="Control"]
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
theme = ExtResource("2")
script = ExtResource("1")

[node name="GuiTransition" type="Node" parent="."]
script = ExtResource("3")
animation_enter = 7
animation_leave = 6
layout = NodePath("..")
group = NodePath("../VBoxContainer2")

[node name="VBoxContainer2" type="VBoxContainer" parent="."]
layout_mode = 0
anchor_right = 1.0
anchor_bottom = 1.0

[node name="Label" type="Label" parent="VBoxContainer2"]
layout_mode = 2
size_flags_horizontal = 3
size_flags_vertical = 3
size_flags_stretch_ratio = 0.2
text = "Delay Algorithm Test"
horizontal_alignment = 1
vertical_alignment = 1

[node name="HBoxContainer" type="HBoxContainer" parent="VBoxContainer2"]
layout_mode = 2
size_flags_horizontal = 3
size_flags_vertical = 3
size_flags_stretch_ratio = 0.2

[node name="LabelDelay" type="Label" parent="VBoxContainer2/HBoxContainer"]
custom_minimum_size = Vector2(180, 0)
layout_mode = 2
size_flags_vertical = 1
text = "Delay"
horizontal_alignment = 1
vertical_alignment = 1
clip_text = true

[node name="SliderDelay" type="HSlider" parent="VBoxContainer2/HBoxContainer"]
custom_minimum_size = Vector2(250, 0)
layout_mode = 2
size_flags_vertical = 1
max_value = 1.0
step = 0.01

[node name="LabelDuration" type="Label" parent="VBoxContainer2/HBoxContainer"]
custom_minimum_size = Vector2(180, 0)
layout_mode = 2
size_flags_vertical = 1
text = "Duration"
horizontal_alignment = 1
vertical_alignment = 1
clip_text = true

[node name="SliderDuration" type="HSlider" parent="VBoxContainer2/HBoxContainer"]
custom_minimum_size = Vector2(250, 0)
layout_mode = 2
size_flags_vertical = 1
min_value = 0.1
max_value = 2.0
step = 0.01
value = 0.1

[node name="MarginContainer" type="MarginContainer" parent="VBoxContainer2"]
layout_mode = 2
size_flags_horizontal = 3
size_flags_vertical = 3
theme_override_constants/margin_left = 8
theme_override_constants/margin_top = 8
theme_override_constants/margin_right = 8
theme_override_constants/margin_bottom = 8

[node name="ContainerControls" type="VBoxContainer" parent="VBoxContainer2/MarginContainer"]
layout_mode = 2
size_flags_horizontal = 3
size_flags_vertical = 3

[node name="MarginContainer2" type="MarginContainer" parent="VBoxContainer2"]
layout_mode = 2
size_flags_horizontal = 3
size_flags_vertical = 3
size_flags_stretch_ratio = 0.4
theme_override_constants/margin_left = 8
theme_override_constants/margin_top = 8
theme_override_constants/margin_right = 8
theme_override_constants/margin_bottom = 8

[node name="HBoxContainer" type="HBoxContainer" parent="VBoxContainer2/MarginContainer2"]
layout_mode = 2
size_flags_horizontal = 3
size_flags_vertical = 3

[node name="TextEditOutput" type="TextEdit" parent="VBoxContainer2/MarginContainer2/HBoxContainer"]
layout_mode = 2
size_flags_horizontal = 3
size_flags_vertical = 3

[node name="ButtonGoTo" type="Button" parent="VBoxContainer2/MarginContainer2/HBoxContainer"]
layout_mode = 2
size_flags_horizontal = 3
size_flags_vertical = 3
size_flags_stretch_ratio = 0.25
focus_mode = 0
text = "Go to layout 1"

[connection signal="value_changed" from="VBoxContainer2/HBoxContainer/SliderDelay" to="." method="_on_SliderDelay_value_changed"]
[connection signal="value_changed" from="VBoxContainer2/HBoxContainer/SliderDuration" to="." method="_on_SliderDuration_value_changed"]
[connection signal="pressed" from="VBoxContainer2/MarginContainer2/HBoxContainer/ButtonGoTo" to="." method="_on_ButtonGoTo_pressed"]
