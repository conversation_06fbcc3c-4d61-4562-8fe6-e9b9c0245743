[gd_scene load_steps=17 format=3 uid="uid://byi87rfqro6rk"]

[ext_resource type="Script" uid="uid://daf3vb4ghs7kc" path="res://effect/batch_particle_root_effect.gd" id="1_nhmie"]
[ext_resource type="Shader" uid="uid://b72uivpctxxxy" path="res://assets/materials/shader/clip_black.gdshader" id="4_n6js6"]
[ext_resource type="Texture2D" uid="uid://cghyxu8emks11" path="res://assets/imgs/effects/50.png" id="5_2cy0t"]

[sub_resource type="ShaderMaterial" id="ShaderMaterial_3xcsc"]
shader = ExtResource("4_n6js6")

[sub_resource type="Curve" id="Curve_opxv8"]
_data = [Vector2(0, 1), 0.0, 0.0, 0, 0, Vector2(0.631148, 1), 0.0, 0.0, 0, 0, Vector2(1, 0), -1.7983, 0.0, 0, 0]
point_count = 3

[sub_resource type="CurveTexture" id="CurveTexture_ko7l2"]
curve = SubResource("Curve_opxv8")

[sub_resource type="Gradient" id="Gradient_u24qa"]
offsets = PackedFloat32Array(0, 0.375576)
colors = PackedColorArray(0.730913, 0.730913, 0.730913, 1, 1, 1, 1, 1)

[sub_resource type="GradientTexture1D" id="GradientTexture1D_nhmie"]
gradient = SubResource("Gradient_u24qa")

[sub_resource type="Curve" id="Curve_hc7ar"]
_data = [Vector2(0, 1), 0.0, 0.0, 0, 0, Vector2(1, 0), 0.0, 0.0, 0, 0]
point_count = 2

[sub_resource type="CurveTexture" id="CurveTexture_ja6mt"]
curve = SubResource("Curve_hc7ar")

[sub_resource type="Curve" id="Curve_u24qa"]
_data = [Vector2(0, 1), 0.0, 0.0, 0, 0, Vector2(0.526639, 0.305584), 0.0, 0.0, 0, 0, Vector2(1, 0), 0.0, 0.0, 0, 0]
point_count = 3

[sub_resource type="CurveTexture" id="CurveTexture_nhmie"]
curve = SubResource("Curve_u24qa")

[sub_resource type="Curve" id="Curve_1mj06"]
_data = [Vector2(0, 1), 0.0, 0.0, 0, 0, Vector2(1, 1), 0.0, 0.0, 0, 0]
point_count = 2

[sub_resource type="CurveTexture" id="CurveTexture_qnp70"]
curve = SubResource("Curve_1mj06")

[sub_resource type="ParticleProcessMaterial" id="ParticleProcessMaterial_8nda6"]
resource_local_to_scene = true
lifetime_randomness = 0.8
particle_flag_disable_z = true
emission_shape = 3
emission_box_extents = Vector3(100, 100, 1)
angle_min = 1.07288e-05
angle_max = 360.0
direction = Vector3(0, 0, 0)
spread = 0.0
radial_velocity_min = 400.0
radial_velocity_max = 400.0
radial_velocity_curve = SubResource("CurveTexture_nhmie")
gravity = Vector3(0, 0, 0)
scale_min = 0.1
scale_max = 0.3
scale_curve = SubResource("CurveTexture_qnp70")
color = Color(0.28892, 0.268078, 0.271554, 1)
color_ramp = SubResource("GradientTexture1D_nhmie")
alpha_curve = SubResource("CurveTexture_ko7l2")
emission_curve = SubResource("CurveTexture_ja6mt")

[sub_resource type="ParticleProcessMaterial" id="ParticleProcessMaterial_jkojd"]
resource_local_to_scene = true
lifetime_randomness = 0.5
particle_flag_disable_z = true
emission_shape_scale = Vector3(1.5, 1.5, 1)
emission_shape = 3
emission_box_extents = Vector3(100, 100, 1)
angle_min = 1.07288e-05
angle_max = 360.0
direction = Vector3(0, -1, 0)
spread = 0.0
initial_velocity_min = 200.0
initial_velocity_max = 400.0
radial_velocity_min = 100.0
radial_velocity_max = 300.0
gravity = Vector3(0, 900, 0)
scale_min = 5.0
scale_max = 10.0
scale_curve = SubResource("CurveTexture_qnp70")
color = Color(0.113889, 0.0798955, 6.01634e-09, 1)
alpha_curve = SubResource("CurveTexture_ko7l2")

[node name="EnemyNormalDie" type="Node2D"]
z_index = -1
script = ExtResource("1_nhmie")

[node name="Smoke" type="GPUParticles2D" parent="."]
material = SubResource("ShaderMaterial_3xcsc")
rotation = 0.575959
skew = 0.450295
emitting = false
amount = 100
texture = ExtResource("5_2cy0t")
one_shot = true
explosiveness = 1.0
randomness = 1.0
collision_base_size = 10.0
local_coords = true
process_material = SubResource("ParticleProcessMaterial_8nda6")

[node name="Splinter" type="GPUParticles2D" parent="."]
emitting = false
amount = 10
lifetime = 0.4
one_shot = true
explosiveness = 1.0
randomness = 1.0
collision_base_size = 10.0
process_material = SubResource("ParticleProcessMaterial_jkojd")
