[gd_resource type="ShaderMaterial" load_steps=8 format=3 uid="uid://clg67pm3b1u8i"]

[ext_resource type="Shader" uid="uid://c1qpfaitcf7lw" path="res://assets/materials/shader/noise_cover.gdshader" id="1_lrjf0"]

[sub_resource type="FastNoiseLite" id="FastNoiseLite_lt4yx"]
frequency = 0.0181

[sub_resource type="NoiseTexture2D" id="NoiseTexture2D_i1wwn"]
seamless = true
noise = SubResource("FastNoiseLite_lt4yx")

[sub_resource type="FastNoiseLite" id="FastNoiseLite_51p13"]
noise_type = 4
frequency = 0.0043
offset = Vector3(-188.84, -310.06, 0)
fractal_type = 3
fractal_lacunarity = 1.74

[sub_resource type="NoiseTexture2D" id="NoiseTexture2D_lk3y4"]
seamless = true
noise = SubResource("FastNoiseLite_51p13")

[sub_resource type="Gradient" id="Gradient_q2pai"]
colors = PackedColorArray(0.28, 0, 0, 1, 0.78, 0, 0, 1)

[sub_resource type="GradientTexture1D" id="GradientTexture1D_w12x5"]
gradient = SubResource("Gradient_q2pai")

[resource]
shader = ExtResource("1_lrjf0")
shader_parameter/noise_pattern = SubResource("NoiseTexture2D_i1wwn")
shader_parameter/noise_pattern2 = SubResource("NoiseTexture2D_lk3y4")
shader_parameter/scroll = Vector2(0, 0)
shader_parameter/scrol2 = Vector2(0, 0.01)
shader_parameter/overlap_color_gradient = SubResource("GradientTexture1D_w12x5")
shader_parameter/color_intensity = 1.885
shader_parameter/threshold = 0.304
shader_parameter/noise_alpha = 0.75
