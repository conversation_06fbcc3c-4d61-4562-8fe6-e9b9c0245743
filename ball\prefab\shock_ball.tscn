[gd_scene load_steps=34 format=3 uid="uid://b1gii88kkua5h"]

[ext_resource type="PhysicsMaterial" uid="uid://d0d4n7iiiu1o8" path="res://assets/materials/physics/ball.tres" id="1_2cc04"]
[ext_resource type="Script" uid="uid://drjqkf3mt3gs1" path="res://ball/ball_base.gd" id="2_1h03u"]
[ext_resource type="Script" uid="uid://boltc4gkngyox" path="res://ui/res/ball_ui_resource.gd" id="3_1h03u"]
[ext_resource type="Script" uid="uid://3lqxnj5nr7f1" path="res://addons/attribute_manager/AttributeComponent.gd" id="3_o7sqr"]
[ext_resource type="Script" uid="uid://coux00joxi7h1" path="res://addons/attribute_manager/Attribute.gd" id="4_y4mis"]
[ext_resource type="Script" uid="uid://doe3ryr4echpc" path="res://attribute/range_random_attribute.gd" id="5_opjjg"]
[ext_resource type="Script" uid="uid://5ww2mn76prh4" path="res://attribute/leveled_attribute.gd" id="6_ukh30"]
[ext_resource type="Script" uid="uid://2dxckbgqoga5" path="res://addons/attribute_manager/AttributeSet.gd" id="7_xv8ni"]
[ext_resource type="Material" uid="uid://bnr828g2v3pnv" path="res://ball/effect/ball_sprite_material/shock_ball.tres" id="8_iigtm"]
[ext_resource type="Texture2D" uid="uid://donaevmmjeptc" path="res://assets/imgs/balls/普通球.png" id="9_2fk72"]
[ext_resource type="Shader" uid="uid://b7lnuflpbunq7" path="res://assets/materials/shader/fluid.gdshader" id="10_2kykh"]
[ext_resource type="Script" uid="uid://c4hm0ggsbq3bl" path="res://addons/trail/trail.gd" id="11_bndfq"]
[ext_resource type="Script" uid="uid://55xeqcwett44" path="res://ball/ability/shock_ability.gd" id="12_mji78"]
[ext_resource type="Script" uid="uid://b14ywpmhnqjgo" path="res://ui/res/ability_ui_resource.gd" id="14_o7sqr"]

[sub_resource type="Resource" id="Resource_o7sqr"]
script = ExtResource("3_1h03u")
ball_name = "地震"
description = "命中伤害 [color=white][min_damage]-[max_damage][/color]"
icon_type = 2
icon_text = ""
icon_shader_material = ExtResource("8_iigtm")
rarity = "普通"
metadata/_custom_type_script = "uid://boltc4gkngyox"

[sub_resource type="Resource" id="Resource_xyqmr"]
script = ExtResource("5_opjjg")
min_value_source = "min_damage"
max_value_source = "max_damage"
base_value = 0.0
can_cache = true
metadata/_custom_type_script = "uid://doe3ryr4echpc"

[sub_resource type="Resource" id="Resource_b4ejh"]
script = ExtResource("4_y4mis")
base_value = 1.0
can_cache = true
metadata/_custom_type_script = "uid://coux00joxi7h1"

[sub_resource type="Resource" id="Resource_x12sx"]
script = ExtResource("6_ukh30")
growth_per_level = 5.0
base_value = 10.0
can_cache = true
metadata/_custom_type_script = "uid://5ww2mn76prh4"

[sub_resource type="Resource" id="Resource_85l4m"]
script = ExtResource("6_ukh30")
growth_per_level = 2.0
base_value = 5.0
can_cache = true
metadata/_custom_type_script = "uid://5ww2mn76prh4"

[sub_resource type="Resource" id="Resource_8ylug"]
script = ExtResource("4_y4mis")
base_value = 400.0
can_cache = true
metadata/_custom_type_script = "uid://coux00joxi7h1"

[sub_resource type="Resource" id="Resource_jh8nh"]
resource_local_to_scene = true
script = ExtResource("7_xv8ni")
attributes = Dictionary[StringName, ExtResource("4_y4mis")]({
&"damage": SubResource("Resource_xyqmr"),
&"level": SubResource("Resource_b4ejh"),
&"max_damage": SubResource("Resource_x12sx"),
&"min_damage": SubResource("Resource_85l4m"),
&"speed": SubResource("Resource_8ylug")
})
metadata/_custom_type_script = "uid://2dxckbgqoga5"

[sub_resource type="FastNoiseLite" id="FastNoiseLite_dlnj0"]
resource_local_to_scene = true
noise_type = 4
seed = -330
frequency = 0.0005
fractal_type = 3
fractal_lacunarity = 2.04
fractal_gain = 4.5
fractal_ping_pong_strength = 4.865

[sub_resource type="NoiseTexture2D" id="NoiseTexture2D_61fgn"]
resource_local_to_scene = true
seamless = true
seamless_blend_skirt = 0.183
noise = SubResource("FastNoiseLite_dlnj0")

[sub_resource type="Gradient" id="Gradient_epvfl"]
offsets = PackedFloat32Array(0, 0.100299, 0.283333, 0.636228, 0.797904, 1)
colors = PackedColorArray(0.126031, 0.0937722, 0, 0.584314, 0, 0, 0, 0.909804, 0, 0, 0, 0.0235294, 1.3, 0.8, 0, 0.702, 0.173753, 0.081227, 1.8049e-08, 1, 1.6, 0.8, 0, 1)

[sub_resource type="GradientTexture1D" id="GradientTexture1D_gese5"]
gradient = SubResource("Gradient_epvfl")
use_hdr = true

[sub_resource type="ShaderMaterial" id="ShaderMaterial_8ylug"]
resource_local_to_scene = true
shader = ExtResource("10_2kykh")
shader_parameter/noise1 = SubResource("NoiseTexture2D_61fgn")
shader_parameter/scroll1 = Vector2(0.15, 0.25)
shader_parameter/scroll2 = Vector2(-0.15, -0.25)
shader_parameter/tex2_scale = 1.0
shader_parameter/overlap_color_gradient = SubResource("GradientTexture1D_gese5")
shader_parameter/overlap_factor = 1.3
shader_parameter/color_factor = 1.1
shader_parameter/blur = 1.0
shader_parameter/delay_v = 0.4
shader_parameter/delay_type = 0
shader_parameter/embed = false
shader_parameter/edge_threshold = 0.29
shader_parameter/edge_softness = 0.01
shader_parameter/edge_noise_scale = 2.5
shader_parameter/edge_noise_influence = 0.31
shader_parameter/edge_noise_scroll = Vector2(0.05, 0.03)
shader_parameter/edge_direction_mode = 0
shader_parameter/use_multiple_edges = true
shader_parameter/edge_left = true
shader_parameter/edge_right = false
shader_parameter/edge_top = true
shader_parameter/edge_bottom = true
shader_parameter/edge_radial = false
shader_parameter/edge_animation_speed = 5.0

[sub_resource type="Curve" id="Curve_yi17e"]
_data = [Vector2(0, 0.248689), 0.0, 0.853875, 0, 0, Vector2(1, 1), 0.0480682, 0.0, 0, 0]
point_count = 2

[sub_resource type="CircleShape2D" id="CircleShape2D_yi17e"]
radius = 19.0263

[sub_resource type="Resource" id="Resource_y4mis"]
script = ExtResource("14_o7sqr")
description = "对附近敌人造成[color=white][min_damage]-[max_damage][/color]伤害"
metadata/_custom_type_script = "uid://b14ywpmhnqjgo"

[sub_resource type="Resource" id="Resource_hrac7"]
script = ExtResource("4_y4mis")
base_value = 1.0
can_cache = true
metadata/_custom_type_script = "uid://coux00joxi7h1"

[sub_resource type="Resource" id="Resource_4p2dw"]
script = ExtResource("6_ukh30")
growth_per_level = 10.0
base_value = 15.0
can_cache = true
metadata/_custom_type_script = "uid://5ww2mn76prh4"

[sub_resource type="Resource" id="Resource_as2aq"]
script = ExtResource("6_ukh30")
growth_per_level = 5.0
base_value = 10.0
can_cache = true
metadata/_custom_type_script = "uid://5ww2mn76prh4"

[sub_resource type="Resource" id="Resource_2rffc"]
resource_local_to_scene = true
script = ExtResource("7_xv8ni")
attributes = Dictionary[StringName, ExtResource("4_y4mis")]({
&"level": SubResource("Resource_hrac7"),
&"max_damage": SubResource("Resource_4p2dw"),
&"min_damage": SubResource("Resource_as2aq")
})
metadata/_custom_type_script = "uid://2dxckbgqoga5"

[node name="ShockBall" type="RigidBody2D"]
collision_layer = 2
collision_mask = 77
physics_material_override = ExtResource("1_2cc04")
gravity_scale = 0.0
can_sleep = false
lock_rotation = true
linear_damp_mode = 1
angular_damp_mode = 1
script = ExtResource("2_1h03u")
ui_resource = SubResource("Resource_o7sqr")
ball_type = 1
cooldown = 15.0
acceleration_multiplier = 0.1

[node name="AttributeComponent" type="Node" parent="."]
script = ExtResource("3_o7sqr")
attribute_set = SubResource("Resource_jh8nh")
metadata/_custom_type_script = "uid://3lqxnj5nr7f1"

[node name="Sprite2D" type="Sprite2D" parent="."]
material = ExtResource("8_iigtm")
scale = Vector2(0.35, 0.35)
texture = ExtResource("9_2fk72")

[node name="Trail" type="Line2D" parent="Sprite2D"]
top_level = true
z_index = -1
texture_repeat = 2
material = SubResource("ShaderMaterial_8ylug")
instance_shader_parameters/overall_alpha = 1.0
width = 30.0
width_curve = SubResource("Curve_yi17e")
texture_mode = 2
joint_mode = 2
begin_cap_mode = 2
end_cap_mode = 2
script = ExtResource("11_bndfq")
max_points = 20

[node name="CollisionShape2D" type="CollisionShape2D" parent="."]
visible = false
shape = SubResource("CircleShape2D_yi17e")

[node name="Abilities" type="Node" parent="."]

[node name="ShockAbility" type="Node" parent="Abilities"]
script = ExtResource("12_mji78")
ui_resource = SubResource("Resource_y4mis")
metadata/_custom_type_script = "uid://55xeqcwett44"

[node name="AttributeComponent" type="Node" parent="Abilities/ShockAbility"]
script = ExtResource("3_o7sqr")
attribute_set = SubResource("Resource_2rffc")
metadata/_custom_type_script = "uid://3lqxnj5nr7f1"
