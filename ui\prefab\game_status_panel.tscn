[gd_scene load_steps=7 format=3 uid="uid://0kbubdtveqvo"]

[ext_resource type="Script" uid="uid://4tbv65pa04hj" path="res://ui/game_status_panel.gd" id="1_37ar5"]
[ext_resource type="Script" uid="uid://bimelmc1ce1b2" path="res://addons/simple-gui-transitions/transition.gd" id="2_2kkmv"]
[ext_resource type="PackedScene" uid="uid://ciq8thrfcx81f" path="res://ui/prefab/layer_bar.tscn" id="3_v7ipw"]
[ext_resource type="PackedScene" uid="uid://cvyjbq3bu0mf3" path="res://ui/prefab/exp.tscn" id="4_ghf7x"]
[ext_resource type="PackedScene" uid="uid://c3aberi5cn35v" path="res://ui/prefab/progress.tscn" id="5_ub7lo"]
[ext_resource type="LabelSettings" uid="uid://dd1cndpj488d5" path="res://ui/res/default_label_settings.tres" id="6_o8bs4"]

[node name="GameStatusPanel" type="Control"]
layout_mode = 3
anchor_top = 0.852
anchor_right = 1.0
anchor_bottom = 0.995
offset_top = -0.560059
offset_bottom = 6.3999
grow_horizontal = 2
grow_vertical = 0
size_flags_vertical = 8
script = ExtResource("1_37ar5")

[node name="GuiTransition" type="Node" parent="."]
script = ExtResource("2_2kkmv")
auto_start = 1
animation_enter = 4
animation_leave = 3
layout = NodePath("..")
controls = Array[NodePath]([NodePath("../Hp"), NodePath("../Exp"), NodePath("../VBoxContainer/HBoxContainer/Label"), NodePath("../VBoxContainer/HBoxContainer/Score"), NodePath("../Progress")])
center_pivot = 1
metadata/_custom_type_script = "uid://bimelmc1ce1b2"

[node name="Hp" parent="." instance=ExtResource("3_v7ipw")]
layout_mode = 1
anchors_preset = 2
anchor_top = 1.0
anchor_right = 0.0
offset_left = 17.0
offset_top = -19.9999
offset_right = 17.0
offset_bottom = -19.9999
grow_horizontal = 1
grow_vertical = 0
rotation = -1.5708
scale = Vector2(0.6, 1)

[node name="Exp" parent="." instance=ExtResource("4_ghf7x")]
layout_mode = 1
anchors_preset = 2
anchor_top = 1.0
anchor_right = 0.0
offset_left = 60.0
offset_top = -20.0
offset_right = 60.0
offset_bottom = -20.0
grow_horizontal = 1
grow_vertical = 0
rotation = -1.5708
scale = Vector2(0.6, 1)

[node name="Progress" parent="." instance=ExtResource("5_ub7lo")]
layout_mode = 1
anchors_preset = 3
anchor_left = 1.0
anchor_top = 1.0
offset_left = -40.0
offset_top = -20.0
offset_right = -40.0
offset_bottom = -20.0
grow_horizontal = 0
grow_vertical = 0
rotation = -1.5708
scale = Vector2(0.6, 1)

[node name="VBoxContainer" type="VBoxContainer" parent="."]
layout_mode = 1
anchors_preset = 7
anchor_left = 0.5
anchor_top = 1.0
anchor_right = 0.5
anchor_bottom = 1.0
offset_left = -242.0
offset_top = -166.0
offset_right = 5388.0
offset_bottom = 1414.0
grow_horizontal = 2
grow_vertical = 0
scale = Vector2(0.1, 0.1)

[node name="HBoxContainer" type="HBoxContainer" parent="VBoxContainer"]
layout_direction = 1
layout_mode = 2
size_flags_horizontal = 4
size_flags_vertical = 8
alignment = 1

[node name="Label" type="Label" parent="VBoxContainer/HBoxContainer"]
layout_mode = 2
text = "分数："
label_settings = ExtResource("6_o8bs4")
vertical_alignment = 1

[node name="Score" type="Label" parent="VBoxContainer/HBoxContainer"]
layout_mode = 2
text = "0"
label_settings = ExtResource("6_o8bs4")
vertical_alignment = 1
