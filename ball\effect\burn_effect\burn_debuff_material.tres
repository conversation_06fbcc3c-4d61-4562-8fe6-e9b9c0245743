[gd_resource type="ShaderMaterial" load_steps=8 format=3 uid="uid://c54kcr0axfmtf"]

[ext_resource type="Shader" uid="uid://c1qpfaitcf7lw" path="res://assets/materials/shader/noise_cover.gdshader" id="1_4m6jg"]

[sub_resource type="FastNoiseLite" id="FastNoiseLite_82pk2"]
noise_type = 0
frequency = 0.0044
fractal_octaves = 10
fractal_lacunarity = 2.44

[sub_resource type="NoiseTexture2D" id="NoiseTexture2D_0trat"]
width = 1027
seamless = true
noise = SubResource("FastNoiseLite_82pk2")

[sub_resource type="FastNoiseLite" id="FastNoiseLite_5o5ap"]
noise_type = 2
frequency = 0.0093
fractal_octaves = 1
fractal_lacunarity = 1.225
fractal_gain = 0.12

[sub_resource type="NoiseTexture2D" id="NoiseTexture2D_xs2cu"]
width = 927
seamless = true
noise = SubResource("FastNoiseLite_5o5ap")

[sub_resource type="Gradient" id="Gradient_j0tcx"]
offsets = PackedFloat32Array(0, 0.270408, 0.442543, 0.755501)
colors = PackedColorArray(0, 0, 0, 0, 0, 0, 0, 1, 1.3, 0.5, 0, 1, 3, 1.1, 0, 1)

[sub_resource type="GradientTexture1D" id="GradientTexture1D_qx6bb"]
resource_local_to_scene = true
gradient = SubResource("Gradient_j0tcx")
use_hdr = true

[resource]
resource_local_to_scene = true
shader = ExtResource("1_4m6jg")
shader_parameter/noise_pattern = SubResource("NoiseTexture2D_0trat")
shader_parameter/noise_pattern2 = SubResource("NoiseTexture2D_xs2cu")
shader_parameter/scroll = Vector2(0, 0.43)
shader_parameter/scrol2 = Vector2(0, 0.765)
shader_parameter/overlap_color_gradient = SubResource("GradientTexture1D_qx6bb")
shader_parameter/color_intensity = 2.0
shader_parameter/threshold = 0.273
shader_parameter/noise_alpha = 1.0
