/*
    Hyper Tunnel from "Sailing Beyond" (demoscene production)
    转换为 Godot 4.4 兼容格式

    原始来源: https://www.youtube.com/watch?v=oITx9xMrAcM&
    https://www.pouet.net/prod.php?which=77899
*/

shader_type canvas_item;

// ========== 基础设置 ==========
/**
 * 全局时间缩放因子
 * 控制整体动画播放速度，影响所有时间相关的动画效果
 * 性能影响：低
 */
uniform float time_scale : hint_range(0.0, 3.0) = 1.0;

/**
 * 渲染分辨率
 * 影响渲染精度和性能，较高分辨率提供更好的视觉质量但消耗更多性能
 * 性能影响：高
 */
uniform vec2 resolution = vec2(1024.0, 768.0);

/**
 * 是否启用音频响应
 * 开启后shader会响应音频输入，产生音频可视化效果
 * 性能影响：低
 */
uniform bool use_audio = false;

/**
 * 音频纹理输入
 * 用于音频可视化的纹理数据
 */
uniform sampler2D channel0;

/**
 * 音频响应强度
 * 控制音频对视觉效果的影响程度，值越大音频响应越明显
 * 性能影响：低
 */
uniform float audio_volume : hint_range(0.0, 2.0) = 0.6;

// ========== 性能控制参数 ==========
/**
 * 光线追踪最大迭代次数
 * 控制光线追踪的精度和质量，较高值提供更好的视觉效果但显著影响性能
 * 建议范围：低端设备 20-50，高端设备 50-150
 * 性能影响：高
 */
uniform int max_iterations : hint_range(10, 200) = 100;

/**
 * 噪声函数复杂度
 * 控制分形噪声的层数，影响细节丰富程度和计算复杂度
 * 较高值产生更丰富的细节但消耗更多性能
 * 性能影响：中
 */
uniform int noise_octaves : hint_range(1, 8) = 4;

/**
 * 最大渲染距离
 * 控制光线追踪的最远距离，影响可视范围和性能
 * 较小值可以提高性能但可能导致远处物体被裁剪
 * 性能影响：中
 */
uniform float max_distance : hint_range(100.0, 2000.0) = 1000.0;

/**
 * 像素精度阈值
 * 控制光线追踪的精度要求，较小值提供更高精度但影响性能
 * 性能影响：中
 */
uniform float pixel_precision : hint_range(0.0001, 0.01) = 0.001;

// ========== 视觉效果控制参数 ==========
/**
 * 隧道变形强度
 * 控制隧道形状的扭曲程度，影响隧道的弯曲和变形效果
 * 性能影响：低
 */
uniform float tunnel_deformation : hint_range(0.0, 5.0) = 1.0;

/**
 * 主色调
 * 控制隧道的主要颜色，影响整体色彩风格
 * 性能影响：低
 */
uniform vec3 primary_color : source_color = vec3(1.0, 0.5, 0.4);

/**
 * 次要色调
 * 控制隧道的次要颜色，用于颜色变化和层次
 * 性能影响：低
 */
uniform vec3 secondary_color : source_color = vec3(0.0, 0.4, 0.5);

/**
 * 整体亮度
 * 控制场景的整体明暗程度
 * 性能影响：低
 */
uniform float brightness : hint_range(0.1, 3.0) = 1.0;

/**
 * 对比度
 * 控制颜色的对比强度，影响视觉冲击力
 * 性能影响：低
 */
uniform float contrast : hint_range(0.1, 3.0) = 1.0;

/**
 * 饱和度
 * 控制颜色的饱和程度，影响色彩鲜艳度
 * 性能影响：低
 */
uniform float saturation : hint_range(0.0, 2.0) = 1.0;

// ========== 动画速度控制参数 ==========
/**
 * 相机移动速度
 * 控制相机在隧道中的前进速度，影响穿越感的强烈程度
 * 性能影响：低
 */
uniform float camera_speed : hint_range(0.0, 200.0) = 100.0;

/**
 * 隧道变形动画速度
 * 控制隧道形状变化的速度，独立于相机移动
 * 性能影响：低
 */
uniform float deformation_speed : hint_range(0.0, 2.0) = 0.14;

/**
 * 相机摇摆速度
 * 控制相机左右摇摆的频率，影响视觉动感
 * 性能影响：低
 */
uniform float camera_sway_speed : hint_range(0.0, 2.0) = 0.11;

/**
 * 噪声动画速度
 * 控制噪声纹理的变化速度，影响表面细节的动态效果
 * 性能影响：低
 */
uniform float noise_animation_speed : hint_range(0.0, 1.0) = 0.05;

// ========== 蒸汽效果控制参数 ==========
/**
 * 蒸汽效果强度
 * 控制蒸汽/雾气效果的强度，影响大气感和深度感
 * 性能影响：中
 */
uniform float steam_intensity : hint_range(0.0, 10.0) = 4.0;

/**
 * 蒸汽颜色
 * 控制蒸汽/雾气的颜色，影响大气色调
 * 性能影响：低
 */
uniform vec3 steam_color : source_color = vec3(0.0, 0.4, 0.5);

/**
 * 蒸汽层数
 * 控制蒸汽效果的计算层数，影响效果复杂度和性能
 * 性能影响：中
 */
uniform int steam_layers : hint_range(5, 50) = 24;

// ========== 相机运动参数 ==========
/**
 * 相机摇摆幅度
 * 控制相机左右摇摆的幅度，影响运动感的强烈程度
 * 性能影响：低
 */
uniform float camera_sway_amplitude : hint_range(0.0, 2.0) = 1.0;

/**
 * 相机俯仰变化
 * 控制相机上下俯仰的变化程度
 * 性能影响：低
 */
uniform float camera_pitch_variation : hint_range(0.0, 2.0) = 0.41;

/**
 * 视野角度
 * 控制相机的视野范围，影响透视效果和沉浸感
 * 性能影响：低
 */
uniform float field_of_view : hint_range(30.0, 120.0) = 70.0;

// ========== 隧道几何参数 ==========
/**
 * 隧道基础半径
 * 控制隧道的基本大小，影响空间感
 * 性能影响：低
 */
uniform float tunnel_base_radius : hint_range(5.0, 50.0) = 18.0;

/**
 * 隧道半径变化
 * 控制隧道半径的变化幅度，影响隧道形状的多样性
 * 性能影响：低
 */
uniform float tunnel_radius_variation : hint_range(50.0, 200.0) = 128.0;

/**
 * 隧道复杂度
 * 控制隧道几何形状的复杂程度，影响视觉丰富度
 * 性能影响：中
 */
uniform float tunnel_complexity : hint_range(0.1, 5.0) = 1.0;

// 常量定义
#define INFINITY 1e32
#define FOG 0.06
#define PI 3.14159265
#define TAU (2.0*PI)
#define PHI 1.618033988749895

// 全局常量
const float vol = 0.0;

// 哈希函数 - 生成伪随机数
float hash12(vec2 p) {
    float h = dot(p, vec2(127.1, 311.7));
    return fract(sin(h) * 43758.5453123);
}

// 3D 噪声函数
float noise_3(in vec3 p) {
    vec3 i = floor(p);
    vec3 f = fract(p);
    vec3 u = f * f * (3.0 - 2.0 * f); // 平滑插值函数

    vec2 ii = i.xy + i.z * vec2(5.0);
    float a = hash12(ii + vec2(0.0, 0.0));
    float b = hash12(ii + vec2(1.0, 0.0));
    float c = hash12(ii + vec2(0.0, 1.0));
    float d = hash12(ii + vec2(1.0, 1.0));
    float v1 = mix(mix(a, b, u.x), mix(c, d, u.x), u.y);

    ii += vec2(5.0);
    a = hash12(ii + vec2(0.0, 0.0));
    b = hash12(ii + vec2(1.0, 0.0));
    c = hash12(ii + vec2(0.0, 1.0));
    d = hash12(ii + vec2(1.0, 1.0));
    float v2 = mix(mix(a, b, u.x), mix(c, d, u.x), u.y);

    return max(mix(v1, v2, u.z), 0.0);
}

// 分形布朗运动 - 多层噪声叠加
float fbm(vec3 x) {
    float r = 0.0;
    float w = 1.0, s = 1.0;
    for (int i = 0; i < noise_octaves; i++) {
        w *= 0.25;
        s *= 3.0;
        r += w * noise_3(s * x);
    }
    return r;
}

// Y轴曲线函数 - 用于隧道变形
float yC(float x) {
    return cos(x * -0.134) * tunnel_deformation * sin(x * 0.13) * 15.0 + fbm(vec3(x * 0.1, 0.0, 0.0) * 55.4) * tunnel_complexity;
}

// 2D旋转函数
void pR(inout vec2 p, float a) {
    p = cos(a) * p + sin(a) * vec2(p.y, -p.x);
}

// 几何体结构
struct geometry {
    float dist;
    vec3 hit;
    int iterations;
};

// 无限高度圆柱体距离函数
float fCylinderInf(vec3 p, float r) {
    return length(p.xz) - r;
}

// 场景映射函数 - 定义3D几何体
geometry map(vec3 p) {
    float current_time = TIME * time_scale;

    p.x -= yC(p.y * 0.1) * 3.0 * tunnel_deformation;
    p.z += yC(p.y * 0.01) * 4.0 * tunnel_deformation;

    float n = pow(abs(fbm(p * 0.06)) * 12.0, 1.3) * tunnel_complexity;
    float s = fbm(p * 0.01 + vec3(0.0, current_time * deformation_speed, 0.0)) * tunnel_radius_variation;

    geometry obj;

    obj.dist = max(0.0, -fCylinderInf(p, s + tunnel_base_radius - n));

    p.x -= sin(p.y * 0.02) * 34.0 * tunnel_deformation + cos(p.z * 0.01) * 62.0 * tunnel_deformation;

    obj.dist = max(obj.dist, -fCylinderInf(p, s + tunnel_base_radius + 10.0 + n * 2.0));

    return obj;
}

// 光线追踪参数
const float t_min = 10.0;

// 光线追踪函数 - 球体追踪算法
geometry trace(vec3 o, vec3 d) {
    float omega = 1.3;
    float t = t_min;
    float candidate_error = INFINITY;
    float candidate_t = t_min;
    float previousRadius = 0.0;
    float stepLength = 0.0;
    float pixelRadius = pixel_precision;

    geometry mp = map(o);

    float functionSign = mp.dist < 0.0 ? -1.0 : 1.0;
    float minDist = max_distance;

    for (int i = 0; i < max_iterations; ++i) {
        mp = map(d * t + o);
        mp.iterations = i;

        float signedRadius = functionSign * mp.dist;
        float radius = abs(signedRadius);
        bool sorFail = omega > 1.0 && (radius + previousRadius) < stepLength;

        if (sorFail) {
            stepLength -= omega * stepLength;
            omega = 1.0;
        } else {
            stepLength = signedRadius * omega;
        }
        previousRadius = radius;
        float error = radius / t;

        if (!sorFail && error < candidate_error) {
            candidate_t = t;
            candidate_error = error;
        }

        if (!sorFail && error < pixelRadius || t > max_distance) break;

        t += stepLength * 0.5;
    }

    mp.dist = candidate_t;

    if (t > max_distance || candidate_error > pixelRadius) {
        mp.dist = INFINITY;
    }

    return mp;
}

void fragment() {
    // 获取当前时间
    float current_time = TIME * time_scale;
    float mt = use_audio ? current_time : current_time; // 简化音频时间处理

    // 计算屏幕坐标
    vec2 fragCoord = UV * resolution;
    vec2 ouv = fragCoord.xy / resolution.xy;
    vec2 uv = ouv - 0.5;

    uv *= tan(radians(field_of_view) / 2.0) * 4.0;

    // 设置相机参数
    vec3 vuv = normalize(vec3(
        cos(current_time) * camera_sway_amplitude,
        sin(current_time * camera_sway_speed),
        sin(current_time * camera_pitch_variation)
    )); // up
    vec3 ro = vec3(0.0, 30.0 + current_time * camera_speed, -0.1);

    ro.x += yC(ro.y * 0.1) * 3.0 * tunnel_deformation;
    ro.z -= yC(ro.y * 0.01) * 4.0 * tunnel_deformation;

    vec3 vrp = vec3(0.0, 50.0 + current_time * camera_speed, 2.0);

    vrp.x += yC(vrp.y * 0.1) * 3.0 * tunnel_deformation;
    vrp.z -= yC(vrp.y * 0.01) * 4.0 * tunnel_deformation;

    // 计算相机矩阵
    vec3 vpn = normalize(vrp - ro);
    vec3 u = normalize(cross(vuv, vpn));
    vec3 v = cross(vpn, u);
    vec3 vcv = (ro + vpn);
    vec3 scrCoord = (vcv + uv.x * u * resolution.x / resolution.y + uv.y * v);
    vec3 rd = normalize(scrCoord - ro);
    vec3 oro = ro;

    vec3 sceneColor = vec3(0.0);

    // 执行光线追踪
    geometry tr = trace(ro, rd);

    tr.hit = ro + rd * tr.dist;

    // 计算基础颜色
    vec3 col = primary_color * fbm(tr.hit.xzy * 0.01) * 20.0;
    col.b *= fbm(tr.hit * 0.01) * 10.0;

    sceneColor += min(0.8, float(tr.iterations) / 90.0) * col + col * 0.03;
    sceneColor *= 1.0 + 0.9 * (abs(fbm(tr.hit * 0.002 + 3.0) * 10.0) * (fbm(vec3(0.0, 0.0, current_time * noise_animation_speed) * 2.0)) * 1.0);

    // 音频响应处理（简化版）
    float audio_response = use_audio ? audio_volume * min(1.0, mt * 0.1) : audio_volume;
    sceneColor = pow(sceneColor, vec3(1.0)) * audio_response * brightness;

    // 添加蒸汽效果
    vec3 steamColor1 = steam_color;
    ro = tr.hit;

    float distC = tr.dist;
    float f = 0.0;
    float st = 0.9;

    for (float i = 0.0; i < float(steam_layers); i++) {
        oro = ro - rd * distC;
        f += fbm(oro * vec3(0.1, 0.1, 0.1) * 0.3) * 0.1;
        distC -= 3.0;
        if (distC < 3.0) break;
    }

    steamColor1 *= use_audio ? audio_volume : 1.0;
    sceneColor += steamColor1 * pow(abs(f * 1.5), 3.0) * steam_intensity;

    // 应用颜色调节
    sceneColor = mix(vec3(dot(sceneColor, vec3(0.299, 0.587, 0.114))), sceneColor, saturation);
    sceneColor = pow(sceneColor, vec3(1.0 / contrast));

    // 最终颜色处理
    vec4 finalColor = vec4(clamp(sceneColor * (1.0 - length(uv) / 2.0), 0.0, 1.0), 1.0);
    finalColor = pow(abs(finalColor / tr.dist * 130.0), vec4(0.8));

    COLOR = finalColor;
}
