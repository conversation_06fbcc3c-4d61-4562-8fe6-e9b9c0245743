shader_type canvas_item;

// 云和星空背景着色器
// 主要通过分形噪声和随机函数生成动态的星云和星星效果

// 控制分形噪声叠加次数，数值越大云越细腻
uniform int OCTAVE = 12;
// 控制动画速度
uniform float timescale = 5.0;
// 云层1的颜色
uniform vec4 CLOUD1_COL: source_color = vec4(0.41,0.64,0.78,0.4);
// 云层2的颜色
uniform vec4 CLOUD2_COL: source_color = vec4(0.99,0.79,0.46,0.2);
// 云层3的颜色
uniform vec4 CLOUD3_COL: source_color = vec4(0.81,0.31,0.59,1.0);
// 云层4的颜色
uniform vec4 CLOUD4_COL: source_color = vec4(0.27,0.15,0.33,1.0);
// 太空背景色
uniform vec4 SPACE: source_color = vec4(0.09,0.06,0.28,0.3);
// 缩放整体云层的尺寸
uniform float zoomScale = 6.0;
// 星星的大小
uniform float size = 10.0;
// 星星分布的缩放系数
uniform float starscale = 20.0;
// 控制星星出现的概率，越大星星越少
uniform float prob: hint_range(0.0,1.0) = 0.98;
// 控制小星星出现的概率，越大越少
uniform float small_star_prob: hint_range(0.0,1.0) = 0.95;

// 生成伪随机数，输入为二维向量
float rand(vec2 input){
	return fract(sin(dot(input,vec2(23.53,44.0)))*42350.45);
}

// 生成简单的二维perlin噪声
float perlin(vec2 input){
	vec2 i = floor(input); // 整数部分
	vec2 j = fract(input); // 小数部分
	vec2 coord = smoothstep(0.,1.,j); // 平滑插值

	float a = rand(i);
	float b = rand(i+vec2(1.0,0.0));
	float c = rand(i+vec2(0.0,1.0));
	float d = rand(i+vec2(1.0,1.0));

	return mix(mix(a,b,coord.x),mix(c,d,coord.x),coord.y);
}

// 分形布朗运动（Fractal Brownian Motion），叠加多层perlin噪声
float fbm(vec2 input){
	float value = 0.0;
	float scale = 0.5;

	for(int i = 0; i < OCTAVE; i++){
		value += perlin(input)*scale;
		input*=2.0;
		scale*=0.5;
	}
	return value;
}

// 生成云的分形噪声，带阈值平滑
float fbmCloud(vec2 input, float minimum){
	float value = 0.0;
	float scale = 0.5;

	for(int i = 0; i < OCTAVE; i++){
		value += perlin(input)*scale;
		input*=2.0;
		scale*=0.5;
	}
	// 两次smoothstep让云边缘更柔和
	return smoothstep(0.,1.,(smoothstep(minimum,1.,value)-minimum)/(1.0-minimum));
}

// 生成云的分形噪声，线性过渡
float fbmCloud2(vec2 input, float minimum){
	float value = 0.0;
	float scale = 0.5;

	for(int i = 0; i < OCTAVE; i++){
		value += perlin(input)*scale;
		input*=2.0;
		scale*=0.5;
	}
	return (smoothstep(minimum,1.,value)-minimum)/(1.0-minimum);
}

void fragment(){
	// 采样原始纹理颜色（如果有贴图）
	vec4 originalColor = texture(TEXTURE, UV);
	// 动画用的时间缩放
	float timescaled = TIME * timescale;
	// 生成不同层次的UV用于云层动画
	// zoomUV2/3/4分别用于不同云层的动态扰动
	vec2 zoomUV2 = vec2(zoomScale * UV.x + 0.03*timescaled*sin(0.07*timescaled), zoomScale * UV.y + 0.03*timescaled*cos(0.06*timescaled));
	vec2 zoomUV3 = vec2(zoomScale * UV.x + 0.027*timescaled*sin(0.07*timescaled), zoomScale * UV.y + 0.025*timescaled*cos(0.06*timescaled));
	vec2 zoomUV4 = vec2(zoomScale * UV.x + 0.021*timescaled*sin(0.07*timescaled), zoomScale * UV.y + 0.021*timescaled*cos(0.07*timescaled));
	// 用于动态调整云层形状的参数
	float tide = 0.05*sin(TIME);
	float tide2 = 0.06*cos(0.3*TIME);
	// 叠加多层云和星空
	vec4 nebulaTexture = vec4(SPACE.rgb, 0.5+0.2*sin(0.23*TIME +UV.x-UV.y)); // 基础太空色
	nebulaTexture += fbmCloud2(zoomUV3, 0.24 + tide)*CLOUD1_COL; // 第一层云
	nebulaTexture += fbmCloud(zoomUV2*0.9, 0.33 - tide)*CLOUD2_COL; // 第二层云
	nebulaTexture = mix(nebulaTexture,CLOUD3_COL,fbmCloud(vec2(0.9*zoomUV4.x,0.9*zoomUV4.y), 0.25+tide2)); // 第三层云
	nebulaTexture = mix(nebulaTexture,CLOUD4_COL,fbmCloud(zoomUV3*0.7+2.0, 0.4+tide2)); // 第四层云
	// 生成星星
	vec2 zoomstar = starscale*zoomUV2;
	vec2 pos = floor(zoomstar / size); // 星星所在格子
	float starValue = rand(pos); // 随机决定是否有星星
	if(starValue>prob){
		// 生成大星星
		vec2 center = size * pos + vec2(size, size) * 0.5; // 星星中心
		float t = 0.9 + 0.2 * sin(TIME * 8.0 + (starValue - prob) / (1.0 - prob) * 45.0); // 星星闪烁动画
		float color = 1.0 - distance(zoomstar, center) / (0.5 * size); // 星星亮度随距离衰减
		nebulaTexture = mix(nebulaTexture, vec4(1.0,1.0,1.0,1.0),smoothstep(0.,1.,color * t / (abs(zoomstar.y - center.y)) * t / (abs(zoomstar.x - center.x))));
	} else {
		// 生成小星星
		zoomstar *= 5.0;
		pos = floor(zoomstar / size);
		float starValue2 = rand(pos + vec2(13.0,13.0));
		if(starValue2 >= small_star_prob){
			vec2 center = size * pos + vec2(size, size) * 0.5;
			float t = 0.9 + 0.2 * sin(TIME * 8.0 + (starValue - prob) / (1.0 - prob) * 45.0);
			float color = 1.0 - distance(zoomstar, center) / (0.5 * size);
			nebulaTexture = mix(nebulaTexture, vec4(1.0,1.0,1.0,1.0),fbmCloud(pos,0.0)*smoothstep(0.,1.,color * t / (abs(zoomstar.y - center.y)) * t / (abs(zoomstar.x - center.x))));
		}
	}
	// 输出最终颜色，alpha固定为1
	COLOR = vec4(nebulaTexture.rgb, 1.0);
	// 如果想要用原始alpha，可以用下面这行：
	// COLOR = vec4(nebulaTexture.rgb,nebulaTexture.a * 1.2)
}