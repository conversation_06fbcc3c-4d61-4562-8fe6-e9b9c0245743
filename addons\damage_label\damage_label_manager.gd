extends Node

## 飘字管理器 (Singleton)
## 负责管理 DamageLabel 实例的对象池，并统一处理它们的动画。

# 导出的变量，可在 Godot 编辑器中设置
@export_group("Spawning")
@export var label_scene: PackedScene
@export var pool_size: int = 200
@export var default_text_color: Color = Color.WHITE

@export_group("Lifetime")
@export var default_duration: float = 1.0

@export_group("Physics")
@export var global_acceleration: Vector2 = Vector2(0, 500)
# 每个实例出生时，在其全局加速度基础上增加的额外随机加速度范围。
@export var random_acceleration_min: Vector2 = Vector2.ZERO
@export var random_acceleration_max: Vector2 = Vector2.ZERO
# 阻尼/减速的随机范围。值越大，减速越快。
@export var damping_min: float = 0.0
@export var damping_max: float = 0.0

@export_group("Initial Velocity")
# 数字初始飞出速度的随机范围
@export var initial_velocity_min: Vector2 = Vector2(-150, -300)
@export var initial_velocity_max: Vector2 = Vector2(150, -500)

@export_group("Animation Curves")
# 控制数字在生命周期内缩放变化的曲线资源
@export var scale_curve: Curve
# 控制数字在生命周期内透明度变化的曲线资源
@export var alpha_curve: Curve

# 对象池和活动实例列表
var _pool: Array[DamageLabel] = []
var _active_labels: Array[DamageLabel] = []


func _ready() -> void:
	# 检查必要的场景是否已设置
	if not label_scene:
		printerr("DamageLabelManager: 'label_scene' is not set. Please assign the DamageLabel.tscn scene in the inspector.")
		return

	# 预先加载对象池
	for i in range(pool_size):
		var instance = label_scene.instantiate() as DamageLabel
		_add_to_pool(instance)


func _process(delta: float) -> void:
	# 从后向前遍历，以便在迭代过程中安全地移除元素
	for i in range(_active_labels.size() - 1, -1, -1):
		var label_instance = _active_labels[i]
		
		# 更新生命周期
		label_instance.lifetime -= delta
		
		if label_instance.lifetime <= 0:
			# 生命周期结束，回收实例
			_active_labels.remove_at(i)
			_add_to_pool(label_instance)
		else:
			# -- 物理更新 --
			# 应用加速度
			label_instance.velocity += label_instance.acceleration * delta
			# 应用阻尼
			if label_instance.damping > 0:
				label_instance.velocity = label_instance.velocity.linear_interpolate(Vector2.ZERO, label_instance.damping * delta)
			# 更新位置
			label_instance.global_position += label_instance.velocity * delta
			
			# -- 动画曲线更新 --
			# 计算生命周期进度 (0.0 -> 1.0)
			var progress = 1.0 - (label_instance.lifetime / label_instance.duration)
			
			# 应用缩放曲线
			if scale_curve:
				label_instance.scale = Vector2.ONE * scale_curve.sample(progress)
			
			# 应用透明度曲线
			if alpha_curve:
				var new_color = label_instance.modulate
				new_color.a = alpha_curve.sample(progress)
				label_instance.modulate = new_color


## Public API: 在指定位置生成一个飘字
func spawn_damage_label( position: Vector2, text: String,options: Dictionary = {}) -> void:
	var label_instance = _get_from_pool()
	
	# 从选项字典或默认值中获取配置
	var duration = options.get("duration", default_duration)
	var color = options.get("color", default_text_color)
	var base_scale = options.get("scale", 1.0) # 基础缩放

	# 配置实例
	label_instance.label.text = text
	label_instance.global_position = position
	label_instance.modulate = color
	label_instance.scale = Vector2.ONE * base_scale # 应用基础缩放
	
	# 设置状态
	label_instance.lifetime = duration
	label_instance.duration = duration
	
	# 设置物理属性
	var rand_vel_x = randf_range(initial_velocity_min.x, initial_velocity_max.x)
	var rand_vel_y = randf_range(initial_velocity_min.y, initial_velocity_max.y)
	label_instance.velocity = Vector2(rand_vel_x, rand_vel_y)

	var rand_accel_x = randf_range(random_acceleration_min.x, random_acceleration_max.x)
	var rand_accel_y = randf_range(random_acceleration_min.y, random_acceleration_max.y)
	label_instance.acceleration = global_acceleration + Vector2(rand_accel_x, rand_accel_y)
	
	label_instance.damping = randf_range(damping_min, damping_max)
	
	# 激活实例
	label_instance.visible = true
	_active_labels.append(label_instance)


# 从池中获取一个实例
func _get_from_pool() -> DamageLabel:
	if _pool.is_empty():
		# 如果池已空，动态创建一个新的实例（健壮性）
		var new_instance = label_scene.instantiate() as DamageLabel
		add_child(new_instance)
		return new_instance
	
	return _pool.pop_front()


# 将实例回收到池中
func _add_to_pool(instance: DamageLabel) -> void:
	instance.visible = false
	# 如果实例尚未成为管理器的子节点，则添加它
	if not instance.get_parent() == self:
		add_child(instance)
	_pool.push_back(instance) 