/*
* Scale and move GUI controls around using uniforms.
* Make materials unique and local to scene to ensure individual transforms.
*
* 缩放变换以纹理中心为原点，确保视觉上的居中效果
*/

shader_type canvas_item;

uniform vec2 slide = vec2(0.0, 0.0);
uniform vec2 scale = vec2(1.0, 1.0);
uniform vec2 node_size = vec2(100.0, 100.0);

void vertex() {
	// 使用传入的节点尺寸计算中心点，而不是依赖TEXTURE_PIXEL_SIZE
	vec2 center = node_size * 0.5;

	// 先应用滑动偏移
	vec2 translated_vertex = VERTEX + slide;

	// 将顶点坐标转换为相对于中心的坐标
	vec2 centered_vertex = translated_vertex - center;

	// 应用缩放变换（以中心为原点）
	vec2 scaled_vertex = centered_vertex * scale;

	// 将坐标转换回原始坐标系
	vec2 final_vertex = scaled_vertex + center;

	VERTEX = final_vertex;
}
