extends Control
class_name GameStatusPanel

@onready var hp_bar: LayerBar = $Hp
@onready var progress_bar: Bar = $Progress
@onready var exp_bar: Bar = $Exp
@onready var score_label: Label = $VBoxContainer/HBoxContainer/Score

var start_score: int = 0


func _ready() -> void:
	GameManager.score_changed.connect(score_changed)
	GameManager.exp_changed.connect(exp_changed)
	GameManager.level_up_exp_changed.connect(level_up_exp_changed)
	GameEvents.player_received_damage.connect(_on_player_received_damage)


func init_bar(hp, max_hp):
	progress_bar.top_bar.max_value = 100.0
	progress_bar.value = 0.0

	exp_bar.top_bar.max_value = GameManager.level_up_exp
	exp_bar.value = 0.0

	hp_bar.top_bar.max_value = max_hp
	hp_bar.mid_bar.max_value = max_hp
	hp_bar.bottom_bar.max_value = max_hp
	hp_bar.value = hp


func score_changed(score: int) -> void:
	var progress = (score - start_score) % 100
	progress_bar.value = progress
	score_label.text = str(score)


func exp_changed(_exp: float) -> void:
	exp_bar.value = _exp


func level_up_exp_changed(level_up_exp: int) -> void:
	exp_bar.top_bar.max_value = level_up_exp


func _on_player_received_damage(player: PlayerManager, damage_info: Dictionary) -> void:
	var hp = damage_info["remaining_hp"]
	var max_hp = damage_info["max_hp"]
	hp_bar.value = (hp/max_hp)*hp_bar.top_bar.max_value
