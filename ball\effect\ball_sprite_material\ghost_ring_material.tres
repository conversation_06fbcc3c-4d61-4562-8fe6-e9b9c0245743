[gd_resource type="ShaderMaterial" load_steps=2 format=3 uid="uid://bsy5vd8ya1x8u"]

[ext_resource type="Shader" uid="uid://ct4hawxs4oup2" path="res://assets/materials/shader/fresnel.gdshader" id="1_kb0qy"]

[resource]
shader = ExtResource("1_kb0qy")
shader_parameter/color = Color(0.54, 0.823667, 1, 1)
shader_parameter/ring_outer_radius = 0.327
shader_parameter/ring_inner_radius = 0.126
shader_parameter/ring_outer_fade_thickness = 0.0
shader_parameter/ring_inner_fade_thickness = 0.191
shader_parameter/animation_speed = 1.0
shader_parameter/animation_magnitude = 0.01
