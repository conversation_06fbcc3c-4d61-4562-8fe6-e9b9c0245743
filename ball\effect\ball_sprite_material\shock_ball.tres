[gd_resource type="ShaderMaterial" load_steps=2 format=3 uid="uid://bnr828g2v3pnv"]

[ext_resource type="Shader" uid="uid://dhretrlifb44d" path="res://assets/materials/shader/lava_ball.gdshader" id="1_mh42v"]

[resource]
shader = ExtResource("1_mh42v")
shader_parameter/radius = 2.0
shader_parameter/noise_amplitude = 0.102
shader_parameter/noise_frequency = 3.5
shader_parameter/speed = 1.0
shader_parameter/glow_intensity_min = 4.0
shader_parameter/glow_intensity_max = 8.0
shader_parameter/glow_flicker_speed = 1.0
shader_parameter/color1 = Color(0.862258, 0.63745, 1.15514e-06, 1)
shader_parameter/color2 = Color(0.702659, 0.574774, 0.345382, 1)
shader_parameter/color3 = Color(0.824, 0.3, 0, 1)
shader_parameter/color4 = Color(0.101984, 0.0498312, 3.30899e-08, 1)
