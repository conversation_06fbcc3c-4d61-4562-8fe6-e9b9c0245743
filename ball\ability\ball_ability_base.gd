class_name BallAbilityBase
extends Node

# 弹球能力基类。所有弹球的能力（如火球、幽灵、分裂等）都应继承自此类。
# 子类可重写相关方法，实现特定效果。
@export var ui_resource: AbilityUIResource

var ball: RigidBody2D

func _ready():
	# owner 是场景树中此节点的拥有者，也就是弹球
	if owner is RigidBody2D:
		ball = owner

# 钩子1: 当弹球与任何物体发生物理碰撞时被调用
# 适合处理：分裂、反弹修改、触发音效等影响弹球自身的行为
func on_ball_collision(collision: KinematicCollision2D):
	pass


# 钩子3: 在每个物理帧被调用
# 适合处理：提供持续性效果，如光环、自动追踪等
func on_physics_update(delta: float) -> void:
	pass


## 当弹球直接命中一个敌人时调用 (主目标命中)。
func on_primary_hit(source: BallBase, target: EnemyBase) -> void:
	pass

## 当被其他能力的效果波及，命中一个次要目标时调用。
func on_secondary_hit(source: BallBase, target: EnemyBase, source_ability: BallAbilityBase) -> void:
	pass


## 获取能力的 AttributeSet（统一接口）
## @return: 能力的 AttributeSet 实例
func get_attribute_set() -> AttributeSet:
	# 模式1：自有 AttributeComponent
	if has_node("AttributeComponent"):
		var attr_comp: AttributeComponent = get_node("AttributeComponent")
		return attr_comp.attribute_set if is_instance_valid(attr_comp) else null
	return null
