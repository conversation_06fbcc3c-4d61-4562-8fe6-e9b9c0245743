extends Button
class_name ItemSlot

## 物品槽UI组件
##
## 用于显示弹球或遗物的槽位，支持：
## - 显示物品图标
## - 空槽状态显示
## - 点击交互
## - 视觉反馈

# 信号定义
signal item_clicked(item_data: UIItemDataBase)
# UI节点引用
@onready var icon_label: Label = $IconLabel
@onready var texture_rect: TextureRect = $TextureRect

# 数据存储
var item_data: UIItemDataBase
var is_empty: bool = true
# 样式常量
const EMPTY_COLOR = Color(0.5, 0.5, 0.5, 0.8)
const FILLED_COLOR = Color.WHITE


func _ready() -> void:
	# 设置初始状态
	set_empty()

	# 设置暂停模式以确保在游戏暂停时仍能接收输入
	process_mode = Node.PROCESS_MODE_WHEN_PAUSED


## 设置物品数据
## @param data: 物品数据字典，包含name、icon、description等信息
func set_item_data(data: UIItemDataBase) -> void:
	item_data = data
	is_empty = false

	# 更新显示
	var item_icon = data.get("icon")
	if item_icon:
		if item_icon is Texture2D:
			icon_label.visible = false
			texture_rect.material = null
			texture_rect.texture = item_icon
			texture_rect.visible = true
		elif item_icon is ShaderMaterial:
			icon_label.visible = false
			texture_rect.texture = CanvasTexture.new()
			texture_rect.material = item_icon
			texture_rect.visible = true
		else:
			texture_rect.material = null
			texture_rect.texture = null
			texture_rect.visible = false
			icon_label.text = item_icon
			icon_label.visible = true

	# 更新按钮状态
	disabled = false
	modulate = FILLED_COLOR


## 设置为空槽状态
func set_empty() -> void:
	item_data = null
	is_empty = true

	# 更新显示
	icon_label.text = ""
	icon_label.visible = false
	texture_rect.texture = null
	texture_rect.material = null
	texture_rect.visible = false

	# 更新按钮状态
	disabled = true
	modulate = EMPTY_COLOR


## 获取物品数据
func get_item_data() -> UIItemDataBase:
	return item_data


## 检查是否为空槽
func is_slot_empty() -> bool:
	return is_empty


## 槽位点击处理
func _on_slot_pressed() -> void:
	if not is_empty:
		print("物品槽被点击：", item_data.get("name"))
		item_clicked.emit(item_data)
	else:
		print("点击了空槽位")
