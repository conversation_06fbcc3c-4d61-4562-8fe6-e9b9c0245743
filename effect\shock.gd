class_name Shock
extends Sprite2D

@onready var timer: Timer = $Timer
@onready var area_2d: Area2D = $Area2D


signal finished

func play():
	self.set_instance_shader_parameter("u_offset", Vector2(randf_range(-100, 100), randf_range(-100, 100)))
	timer.start()


func get_range_enemies() -> Array[EnemyBase]:
	await get_tree().physics_frame
	var bodies = area_2d.get_overlapping_bodies()
	var enemies: Array[EnemyBase] = []
	for body in bodies:
		if body is EnemyBase:
			enemies.append(body)
	return enemies


func _on_timer_timeout():
	finished.emit()
