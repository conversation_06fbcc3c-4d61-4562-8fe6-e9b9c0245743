class_name GhostAbility
extends BallAbilityBase

var damage_area: Area2D # 用来持有我们动态创建的Area2D

func _ready():
	super._ready() # 确保 ball 变量被正确赋值

	# --- 动态创建和配置 ---
	# 1. 创建 Area2D 节点
	damage_area = Area2D.new()
	damage_area.name = "GhostDamageArea" # 方便调试时在远程场景树中识别

	# 2. 获取弹球主体的碰撞体形状，复制给 Area2D
	var main_shape_owner = ball.find_child("CollisionShape2D")
	if main_shape_owner:
		var new_shape = CollisionShape2D.new()
		new_shape.shape = main_shape_owner.shape.duplicate() # 复制形状资源
		damage_area.add_child(new_shape)
	else:
		push_error("GhostAbility couldn't find a CollisionShape2D on the ball.")
		return

	# 3. 设置 Area2D 的物理层和遮罩
	damage_area.collision_layer = 2 # 与弹球在同一层
	damage_area.collision_mask = (1 << 2) | (1 << 3)

	# 4. 连接信号
	damage_area.body_entered.connect(_on_damage_area_body_entered)

	# 5. 将配置好的 Area2D 添加为弹球的子节点
	ball.add_child.call_deferred(damage_area)

	# --- 修改弹球自身的物理属性 ---
	# 让弹球的物理身体忽略敌人层
	ball.set_collision_layer_value(5, true)
	ball.set_collision_mask_value(1, true)
	ball.set_collision_mask_value(3, false)
	ball.set_collision_mask_value(4, false)


func _on_damage_area_body_entered(body: Node2D):
	# 检查进入的是否是敌人
	if body is EnemyBase:
		# 手动调用敌人身上的标准受伤函数
		body.on_hit_by_ball(ball)
