[gd_resource type="ShaderMaterial" load_steps=4 format=3 uid="uid://d3juvi1g0wppy"]

[ext_resource type="Shader" uid="uid://cmp86v6y61wt1" path="res://assets/materials/shader/frosted_glass.gdshader" id="1_ps1yu"]

[sub_resource type="FastNoiseLite" id="FastNoiseLite_k2ivv"]
frequency = 1.0

[sub_resource type="NoiseTexture2D" id="NoiseTexture2D_4xkry"]
noise = SubResource("FastNoiseLite_k2ivv")

[resource]
shader = ExtResource("1_ps1yu")
shader_parameter/effect_mode = 0
shader_parameter/alpha = 1.0
shader_parameter/distortion_strength = 0.638
shader_parameter/noise_distortion = 0.01
shader_parameter/noise_scale = 0.1
shader_parameter/blur_radius = 0.0
shader_parameter/blur_quality = 0
shader_parameter/noise_texture = SubResource("NoiseTexture2D_4xkry")
shader_parameter/use_noise_texture = false
