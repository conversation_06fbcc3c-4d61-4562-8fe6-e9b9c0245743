[gd_scene load_steps=14 format=3 uid="uid://ciq8thrfcx81f"]

[ext_resource type="Script" uid="uid://0kcaylxt4ki0" path="res://ui/prefab/layer_bar.gd" id="1_0j4v8"]
[ext_resource type="Shader" uid="uid://c4ijmxnenptwl" path="res://assets/materials/shader/fluid.tres" id="2_taltp"]
[ext_resource type="Texture2D" uid="uid://u5ndsxu34wr3" path="res://assets/imgs/balls/128x128.png" id="3_iujsj"]

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_quv1s"]
bg_color = Color(0, 0, 0, 1)
expand_margin_left = 1.0
expand_margin_top = 1.0
expand_margin_right = 1.0
expand_margin_bottom = 1.0
shadow_color = Color(0, 0, 0, 0.468)
shadow_size = 5

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_ripfv"]
bg_color = Color(0.552956, 0.552956, 0.552956, 1)

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_iujsj"]

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_t2cmy"]
bg_color = Color(0.552941, 0.552941, 0.552941, 1)

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_y3kyf"]

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_6kbvw"]
bg_color = Color(0, 0.652339, 0.443407, 1)

[sub_resource type="NoiseTexture2D" id="NoiseTexture2D_iujsj"]

[sub_resource type="FastNoiseLite" id="FastNoiseLite_de7si"]
noise_type = 3
frequency = 0.0022
offset = Vector3(133.33, 436.02, 0)
fractal_type = 2
fractal_lacunarity = 1.0
fractal_gain = 0.515

[sub_resource type="NoiseTexture2D" id="NoiseTexture2D_xr0vd"]
resource_local_to_scene = true
width = 2212
seamless = true
seamless_blend_skirt = 1.0
noise = SubResource("FastNoiseLite_de7si")

[sub_resource type="ShaderMaterial" id="ShaderMaterial_t2cmy"]
resource_local_to_scene = true
shader = ExtResource("2_taltp")
shader_parameter/BaseColor = Color(1, 0.12549, 0.086274, 1)
shader_parameter/BaseColor2 = Color(0.4, 0, 0, 1)
shader_parameter/WaveNoise = SubResource("NoiseTexture2D_iujsj")
shader_parameter/OffsetSpeed = 0.05
shader_parameter/OffsetDir = Vector2(1, 0)
shader_parameter/offset = SubResource("NoiseTexture2D_xr0vd")
shader_parameter/EdgeWaveTense = 5.0
shader_parameter/EdgeWaveSpeed = 0.275
shader_parameter/EdgeWaveScale = 0.5
shader_parameter/EdgeWaveLevel = 50.0

[node name="LayerBar" type="Control"]
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
script = ExtResource("1_0j4v8")

[node name="Bg" type="ProgressBar" parent="."]
custom_minimum_size = Vector2(240, 0)
offset_right = 240.0
offset_bottom = 27.0
theme_override_styles/background = SubResource("StyleBoxFlat_quv1s")
theme_override_styles/fill = SubResource("StyleBoxFlat_ripfv")
show_percentage = false

[node name="Bottom" type="ProgressBar" parent="."]
custom_minimum_size = Vector2(240, 0)
layout_mode = 0
offset_right = 240.0
offset_bottom = 27.0
theme_override_styles/background = SubResource("StyleBoxEmpty_iujsj")
theme_override_styles/fill = SubResource("StyleBoxFlat_t2cmy")
value = 70.0
show_percentage = false

[node name="Mid" type="ProgressBar" parent="."]
custom_minimum_size = Vector2(240, 0)
layout_mode = 0
offset_right = 240.0
offset_bottom = 27.0
theme_override_styles/background = SubResource("StyleBoxEmpty_y3kyf")
theme_override_styles/fill = SubResource("StyleBoxFlat_6kbvw")
value = 60.0
show_percentage = false

[node name="Top" type="TextureProgressBar" parent="."]
texture_repeat = 1
material = SubResource("ShaderMaterial_t2cmy")
custom_minimum_size = Vector2(240, 0)
layout_mode = 0
offset_right = 240.0
offset_bottom = 27.0
mouse_filter = 0
step = 0.01
value = 50.0
nine_patch_stretch = true
stretch_margin_left = 1
stretch_margin_top = 1
stretch_margin_right = 1
stretch_margin_bottom = 1
texture_progress = ExtResource("3_iujsj")

[node name="MidTimer" type="Timer" parent="."]
wait_time = 0.4
one_shot = true

[node name="BottomTimer" type="Timer" parent="."]
wait_time = 0.4
one_shot = true

[connection signal="timeout" from="MidTimer" to="." method="_on_mid_timer_timeout"]
[connection signal="timeout" from="BottomTimer" to="." method="_on_bottom_timer_timeout"]
