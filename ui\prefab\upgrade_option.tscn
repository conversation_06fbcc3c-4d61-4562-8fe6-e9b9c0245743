[gd_scene load_steps=4 format=3 uid="uid://c8h4vn2lal5xt"]

[ext_resource type="Material" uid="uid://yuj6qn0q5oh5" path="res://ball/effect/ball_sprite_material/blood_ball.tres" id="2_q4ubo"]
[ext_resource type="Script" uid="uid://bmnchs6jcta3x" path="res://ui/prefab/upgrade_option.gd" id="2_script"]

[sub_resource type="CanvasTexture" id="CanvasTexture_ne14b"]

[node name="UpgradeOption" type="Button"]
custom_minimum_size = Vector2(100, 150)
size_flags_horizontal = 4
size_flags_vertical = 0
theme_type_variation = &"IconSlot"
action_mode = 0
script = ExtResource("2_script")

[node name="VBoxContainer" type="VBoxContainer" parent="."]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
alignment = 1

[node name="Panel" type="Panel" parent="VBoxContainer"]
custom_minimum_size = Vector2(80, 80)
layout_mode = 2
size_flags_horizontal = 4
size_flags_vertical = 6
mouse_filter = 2
theme_type_variation = &"ItemSlotPanelSub"

[node name="TextureRect" type="TextureRect" parent="VBoxContainer/Panel"]
material = ExtResource("2_q4ubo")
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
size_flags_horizontal = 4
size_flags_vertical = 4
mouse_filter = 2
texture = SubResource("CanvasTexture_ne14b")
expand_mode = 1
stretch_mode = 5

[node name="NameLabel" type="Label" parent="VBoxContainer"]
custom_minimum_size = Vector2(0, 30)
layout_mode = 2
size_flags_horizontal = 4
theme_override_font_sizes/font_size = 24
text = "新"
horizontal_alignment = 1
