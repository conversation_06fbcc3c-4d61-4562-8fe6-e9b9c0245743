shader_type canvas_item;

uniform vec2 center = vec2(0.5, 0.5);
uniform vec4 outer_color : source_color = vec4(0.0, 0.0, 0.0, 1.0);
uniform float core_radius : hint_range(0.0, 0.5, 0.01) = 0.1;
uniform float outer_ring_width : hint_range(0.0, 0.5, 0.01) = 0.1;

void fragment() {
	// Euclidean distance for a circular shape
	float dist = distance(center, UV);

    // The gradient starts at the edge of the core
	float gradient_start = core_radius;
    // The gradient ends where the outer ring begins
	float gradient_end = 0.5 - outer_ring_width;

    // Ensure the gradient has a valid range, otherwise create a hard edge
    gradient_end = max(gradient_start, gradient_end);
	
	// Calculate the smooth transition factor (0 at center, 1 at edge)
    float t = smoothstep(gradient_start, gradient_end, dist);
    
    // In canvas_item shaders, COLOR is pre-filled with texture_color * modulate_color.
    // We use this as our base color.
    vec4 base_color = COLOR;
    
    // Mix from the base_color (at the center) to the outer_color (at the edges)
	vec4 final_color = mix(base_color, outer_color, t);
	
	// Ensure the final alpha is based on the original modulated texture's alpha
	final_color.a = base_color.a;

	COLOR = final_color;
}


