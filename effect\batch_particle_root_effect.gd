extends Node2D

signal finished
var count: int = 0
var bounds: Rect2 = Rect2(1.0, 1.0, 0, 0)

func play():
	count = 0
	for child: GPUParticles2D in get_children():
		if child.has_signal("finished") and not child.has_connections("finished"):
			child.finished.connect(child_finished)

		if bounds.size.x > 0 and bounds.size.y > 0:
			child.scale = Vector2(bounds.position.x, bounds.position.y)
			var _material: ParticleProcessMaterial = child.process_material
			var _emission_shape: ParticleProcessMaterial.EmissionShape = _material.emission_shape
			match _emission_shape:
				ParticleProcessMaterial.EMISSION_SHAPE_BOX:
					_material.emission_box_extents = Vector3(bounds.size.x / 2, bounds.size.y / 2, 0)
				ParticleProcessMaterial.EMISSION_SHAPE_SPHERE:
					_material.emission_sphere_radius = min(bounds.size.x, bounds.size.y) / 2
		child.restart()

func child_finished() -> void:
	count += 1
	if count < get_child_count():
		return
	bounds = Rect2(1.0, 1.0, 0, 0)
	finished.emit()
