---
description: 敌人系统实现原理
globs: 
alwaysApply: false
---
---
# 敌人系统实现原理

## 整体架构
- 敌人基于AnimatableBody2D实现，由多个组件模块化组成。
- 使用BoxShape绘制伪3D形状，EnemyVis管理视觉表现，EnemySpawn控制生成逻辑。
- 通过SubViewport + ShadersSprite2D组合渲染处理复杂视觉效果和状态变化。

## BoxShape伪3D绘制
- 使用3x3网格位标志(row_1/row_2/row_3)定义基础形状。
- 通过透视计算生成深度和侧面，创造3D错觉。
- 支持多种颜色、纹理、阴影和轮廓控制。
- 按深度顺序绘制顶面、侧面和内部，实现正确遮挡关系。

## 敌人生成机制
- 采用网格管理系统，使用状态位表示空闲/占用/预占用。
- 实现预占用机制，避免敌人与现有物体重叠。
- 根据难度动态调整生成波次和密度。
- 根据格子配置(grid_config)自动生成碰撞体和阴影。

## 视觉效果处理
- 使用LightOccluder2D作为遮罩，使敌人精灵能够"嵌入"3D形状。
- 震动效果通过Tween实现，包括形状和内部精灵的协调动画。
- 支持血液、燃烧、中毒等多种状态效果，通过BuffHandlers系统处理。

## 属性与伤害系统
- 使用AttributeComponent管理血量、伤害值等属性。
- 支持显示伤害数字和受击闪烁效果。
- 死亡时播放弹跳旋转动画并生成粒子效果。

## 交互逻辑
- 敌人下落到底部会对玩家造成伤害并消失。
- 被弹球击中时减少生命值并显示相应特效。
- 集成buff系统支持DOT伤害和效果扩散。


