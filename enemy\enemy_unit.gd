class_name EnemyUnit
extends AnimatedSprite2D

## 敌人单元 - 独立的攻击单位，放置在敌人底座的格子中
@onready var attribute_component: AttributeComponent = $AttributeComponent

# 攻击系统配置
@export_group("Attack System")
## 是否可以主动攻击
@export var can_attack: bool = false

## 攻击范围（像素）
var attack_range: float:
	get:
		return attribute_component.get_attribute_value("attack_range")
	set(value):
		attribute_component.set_attribute_value("attack_range", value)

## 攻击间隔最小值（秒）
var attack_interval_min: float:
	get:
		return attribute_component.get_attribute_value("attack_interval_min")
	set(value):
		attribute_component.set_attribute_value("attack_interval_min", value)

## 攻击间隔最大值（秒）
var attack_interval_max: float:
	get:
		return attribute_component.get_attribute_value("attack_interval_max")
	set(value):
		attribute_component.set_attribute_value("attack_interval_max", value)

@export_group("Bullet Properties")
## 子弹场景
@export var bullet_scene: PackedScene

# 状态管理
var is_active: bool = true
signal unit_attacking(unit: EnemyUnit)


func _ready():
	# 播放idle动画
	if sprite_frames and sprite_frames.has_animation("idle"):
		play("idle")


func attack(pos: Vector2, direction: Vector2) -> void:
	if not is_active:
		return
	_play_attack_animation()
	unit_attacking.emit(self)
	var bullet = bullet_scene.instantiate()
	bullet.init(pos, direction)
	get_tree().current_scene.add_child(bullet)


## 播放攻击动画
func _play_attack_animation():
	if sprite_frames and sprite_frames.has_animation("attack"):
		play("attack")
		# 动画播放完毕后回到idle状态
		if not animation_finished.is_connected(_on_attack_animation_finished):
			animation_finished.connect(_on_attack_animation_finished, CONNECT_ONE_SHOT)


## 攻击动画完成回调
func _on_attack_animation_finished():
	if sprite_frames and sprite_frames.has_animation("idle"):
		play("idle")


## 停用单元（当敌人死亡时调用）
func deactivate():
	is_active = false


## 激活单元
func activate():
	is_active = true


## 设置攻击配置（由 EnemyVis 调用）
func set_attack_config(config: Dictionary):
	if config.has("attack_range"):
		attack_range = config.attack_range
	if config.has("attack_interval_min"):
		attack_interval_min = config.attack_interval_min
	if config.has("attack_interval_max"):
		attack_interval_max = config.attack_interval_max
