class_name RichTextProcessor
extends RefCounted


static func process_rich_text(text: String, node: Object) -> String:
	if text == "":
		return text

	# 使用统一的属性预览处理
	if AttributeSetProvider.has_attribute_system(node):
		return _replace_unified_attribute_placeholders(text, node)

	return text


## 统一的属性占位符替换方法
## 支持所有有属性系统的节点：弹球、能力、遗物等
## @param text: 文本内容
## @param node: 节点实例
## @return: 替换后的文本
static func _replace_unified_attribute_placeholders(text: String, node: Object) -> String:
	var processed_text: String = text

	# 使用正则表达式查找所有属性占位符，支持等级偏移
	# 格式：[attribute_name] 或 [attribute_name+N] 或 [attribute_name-N]
	var regex = RegEx.new()
	if regex.compile("\\[([a-zA-Z_][a-zA-Z0-9_]*)([+-]\\d+)?\\]") != OK:
		push_error("RichTextProcessor: 属性占位符正则表达式编译失败")
		return processed_text

	var matches = regex.search_all(processed_text)
	if matches.size() == 0:
		return processed_text

	# 从后往前替换，避免位置偏移问题
	for i in range(matches.size() - 1, -1, -1):
		var match_obj = matches[i]
		var attr_name = match_obj.get_string(1)
		var level_offset_str = match_obj.get_string(2)

		# 跳过常见的BBCode标签
		if _is_bbcode_tag(attr_name):
			continue

		var replacement_value: String

		# 解析等级偏移，使用统一的 AttributeSetProvider
		if level_offset_str != "":
			var level_offset: int = level_offset_str.to_int()
			var value = AttributeSetProvider.get_attribute_value_with_offset(node, attr_name, level_offset)
			replacement_value = _format_value(value)
		else:
			var value = AttributeSetProvider.get_attribute_value(node, attr_name)
			replacement_value = _format_value(value)

		# 执行替换
		var start_pos = match_obj.get_start()
		var end_pos = match_obj.get_end()
		processed_text = processed_text.substr(0, start_pos) + replacement_value + processed_text.substr(end_pos)

	return processed_text


## 检查是否为BBCode标签
## @param tag_name: 标签名
## @return: 是否为BBCode标签
static func _is_bbcode_tag(tag_name: String) -> bool:
	var bbcode_tags: Array[Variant] = [
	"b", "i", "u", "s", "code", "center", "right", "left",
	"color", "bgcolor", "font", "size", "url", "img", "table",
	"cell", "tr", "td", "th", "ol", "ul", "li", "indent",
	"wave", "tornado", "shake", "fade", "rainbow"
	]

	return tag_name.to_lower() in bbcode_tags


## 格式化值为字符串
## @param value: 要格式化的值
## @return: 格式化后的字符串
static func _format_value(value: Variant) -> String:
	if value is float:
		# 如果是整数值的浮点数，显示为整数
		if abs(value - round(value)) < 0.001:
			return str(int(round(value)))
		# 否则显示一位小数
		return "%.1f" % value
	elif value is int:
		return str(value)
	else:
		return str(value)


## 移除所有BBCode标签，获取纯文本
## @param rich_text: 富文本
## @return: 纯文本
static func strip_bbcode(rich_text: String) -> String:
	var processed: String = rich_text

	# 移除常见的BBCode标签
	processed = processed.replace("[b]", "")
	processed = processed.replace("[/b]", "")
	processed = processed.replace("[i]", "")
	processed = processed.replace("[/i]", "")
	processed = processed.replace("[center]", "")
	processed = processed.replace("[/center]", "")
	processed = processed.replace("[/color]", "")

	# 移除颜色标签（简化版本）
	var start_pos = processed.find("[color=")
	while start_pos != -1:
		var end_pos = processed.find("]", start_pos)
		if end_pos != -1:
			processed = processed.substr(0, start_pos) + processed.substr(end_pos + 1)
		else:
			break
		start_pos = processed.find("[color=")

	return processed


## 检查文本是否包含BBCode标签
## @param text: 文本内容
## @return: 是否包含BBCode
static func has_bbcode(text: String) -> bool:
	return "[" in text and "]" in text
