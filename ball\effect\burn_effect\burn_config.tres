[gd_resource type="Resource" script_class="AttributeBuffDOT" load_steps=11 format=3 uid="uid://8giqogidxinh"]

[ext_resource type="Script" uid="uid://coux00joxi7h1" path="res://addons/attribute_manager/Attribute.gd" id="1_dlnrx"]
[ext_resource type="Script" uid="uid://5ivjabbc5wk6" path="res://addons/attribute_manager/AttributeBuffDOT.gd" id="1_hh1s2"]
[ext_resource type="Script" uid="uid://5ww2mn76prh4" path="res://attribute/leveled_attribute.gd" id="2_dlnrx"]
[ext_resource type="Script" uid="uid://2dxckbgqoga5" path="res://addons/attribute_manager/AttributeSet.gd" id="2_ko4h2"]

[sub_resource type="Resource" id="Resource_dlnrx"]
script = ExtResource("1_dlnrx")
base_value = 10.0
can_cache = true

[sub_resource type="Resource" id="Resource_55pug"]
script = ExtResource("1_dlnrx")
base_value = 1.0
can_cache = true
metadata/_custom_type_script = "uid://coux00joxi7h1"

[sub_resource type="Resource" id="Resource_ko4h2"]
script = ExtResource("1_dlnrx")
base_value = 3.0
can_cache = true

[sub_resource type="Resource" id="Resource_nnspl"]
script = ExtResource("1_dlnrx")
base_value = 1.0
can_cache = true

[sub_resource type="Resource" id="Resource_lv03r"]
script = ExtResource("2_dlnrx")
growth_per_level = 10.0
base_value = 5.0
can_cache = true
metadata/_custom_type_script = "uid://5ww2mn76prh4"

[sub_resource type="Resource" id="Resource_l52as"]
resource_local_to_scene = true
script = ExtResource("2_ko4h2")
attributes = Dictionary[StringName, ExtResource("1_dlnrx")]({
&"duration": SubResource("Resource_dlnrx"),
&"level": SubResource("Resource_55pug"),
&"max_stacks": SubResource("Resource_ko4h2"),
&"period": SubResource("Resource_nnspl"),
&"value": SubResource("Resource_lv03r")
})

[resource]
resource_local_to_scene = true
script = ExtResource("1_hh1s2")
buff_name = "burn"
operation = 1
policy = 1
merging = 0
attribute_set = SubResource("Resource_l52as")
metadata/_custom_type_script = "uid://5ivjabbc5wk6"
