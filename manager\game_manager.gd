extends Node

var level_up_exp: int = 100
var score: int = 0
var experience: float = 0
var level: int = 1

var current_theme: EnvironmentTheme = null

var enemy_hp_ratio: float = 1.0
var enemy_spawn_ratio: float = 0.000001

signal score_changed(score: int)
signal exp_changed(exp: float)
signal level_up_exp_changed(level_up_exp: int)
signal level_up()

func add_score(value: int):
	score += value
	score_changed.emit(score)

func add_exp(value: float):
	experience += value
	var loop_count: int = 0
	var max_loops: int = 20  # 防止无限循环的安全措施

	while experience >= level_up_exp and loop_count < max_loops:
		experience -= level_up_exp
		level += 1
		level_up_exp = max(floor(1.1 * level_up_exp), 1)  # 确保 level_up_exp 至少为 1
		level_up.emit()
		loop_count += 1

		# 如果达到最大循环次数，记录警告
		if loop_count >= max_loops:
			print("警告: add_exp 循环次数达到上限，可能存在无限循环风险")
			break

	level_up_exp_changed.emit(level_up_exp)
	exp_changed.emit(experience)

func get_enemy_hp(base_hp: int) -> int:
	return base_hp + int(base_hp * enemy_hp_ratio)


func get_enemy_spawn_rate() -> float:
	var rate = 0.1 + score * enemy_spawn_ratio
	return min(rate, 1)
