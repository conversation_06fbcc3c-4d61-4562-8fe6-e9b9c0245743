[gd_scene load_steps=9 format=3 uid="uid://bjuk4xs6y84rq"]

[ext_resource type="Shader" uid="uid://dqueevxr81bom" path="res://addons/damage_number_mesh/tonOfDamageText.gdshader" id="1_rhr7j"]
[ext_resource type="Texture2D" uid="uid://do784nokwoab2" path="res://addons/damage_number_mesh/nums.png" id="2_1tjwi"]
[ext_resource type="Script" uid="uid://fwy7bdcu4as4" path="res://addons/damage_number_mesh/damage_mesh_manager.gd" id="3_rhr7j"]

[sub_resource type="ShaderMaterial" id="ShaderMaterial_wo7hd"]
shader = ExtResource("1_rhr7j")
shader_parameter/numbersTex = ExtResource("2_1tjwi")
shader_parameter/digit_spacing = 0.1
shader_parameter/uv_inset = Vector2(0, 0)
shader_parameter/stroke_thickness = 1.0
shader_parameter/stroke_color = Color(0, 0, 0, 1)
shader_parameter/pattern = 0
shader_parameter/inside = false

[sub_resource type="MultiMesh" id="MultiMesh_4l2se"]
use_colors = true
use_custom_data = true
visible_instance_count = 0

[sub_resource type="Curve" id="Curve_rhr7j"]
_data = [Vector2(0, 0), 0.0, 0.0, 0, 0, Vector2(0.124431, 1), 0.0, 0.0, 0, 0, Vector2(0.165402, 0.624722), -0.132323, -0.132323, 0, 0, Vector2(1, 0.517271), -0.336973, 0.0, 0, 0]
point_count = 4

[sub_resource type="Curve" id="Curve_1tjwi"]
_data = [Vector2(0, 1), 0.0, 0.0, 0, 0, Vector2(0.74772, 1), 0.0, 0.0, 0, 0, Vector2(1, 0), -2.23261, 0.0, 0, 0]
point_count = 3

[node name="DamageManager" type="MultiMeshInstance2D"]
material = SubResource("ShaderMaterial_wo7hd")
multimesh = SubResource("MultiMesh_4l2se")
script = ExtResource("3_rhr7j")
scale_curve = SubResource("Curve_rhr7j")
alpha_curve = SubResource("Curve_1tjwi")
