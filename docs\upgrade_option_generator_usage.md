# 升级选项生成器使用指南

## 概述

升级选项生成器（UpgradeOptionGenerator）是一个用于管理升级选项稀有度和权重系统的工具类。它提供了统一的稀有度等级系统、权重配置管理和批量调整功能。

## 核心功能

### 1. 稀有度等级系统

系统支持四个稀有度等级：
- **普通** (Common) - 白色
- **稀有** (Rare) - 绿色  
- **史诗** (Epic) - 蓝色
- **传说** (Legendary) - 紫色

### 2. 权重配置系统

每个物品（弹球或遗物）都可以配置：
- 唯一标识符 (item_id)
- 稀有度等级 (rarity)
- 权重值 (weight) - 必须 >= 0
- 物品类型 (item_type) - "ball" 或 "relic"

### 3. 批量调整功能

支持按稀有度等级批量调整权重：
- 加法调整：增加固定数值
- 乘法调整：乘以倍数
- 设置固定值：统一设置为指定权重

## 基本使用方法

### 创建生成器实例

```gdscript
var generator = UpgradeOptionGenerator.new()
```

### 添加物品权重配置

```gdscript
# 手动添加配置
generator.set_item_weight_config(
    "fire_ball",                                    # 物品ID
    UpgradeOptionGenerator.RarityLevel.COMMON,      # 稀有度
    10.0,                                           # 权重
    "ball"                                          # 类型
)

# 从弹球实例自动添加
var ball_instance = preload("res://ball/prefab/poison_ball.tscn").instantiate()
generator.add_ball_weight_config(ball_instance, 10.0)

# 从遗物实例自动添加
var relic_resource = preload("res://relic/relics/iron_armor.tres")
generator.add_relic_weight_config(relic_resource, 8.0)
```

### 查询和筛选

```gdscript
# 获取特定物品配置
var config = generator.get_item_weight_config("fire_ball")

# 按稀有度筛选
var common_items = generator.get_configs_by_rarity(UpgradeOptionGenerator.RarityLevel.COMMON)

# 按类型筛选
var ball_items = generator.get_configs_by_type("ball")

# 获取所有配置
var all_configs = generator.get_all_weight_configs()
```

### 批量调整权重

```gdscript
# 将所有普通稀有度物品权重增加5.0
generator.adjust_rarity_weights_add(UpgradeOptionGenerator.RarityLevel.COMMON, 5.0)

# 将所有稀有稀有度物品权重乘以1.5
generator.adjust_rarity_weights_multiply(UpgradeOptionGenerator.RarityLevel.RARE, 1.5)

# 将所有史诗稀有度物品权重设置为100.0
generator.set_rarity_weights(UpgradeOptionGenerator.RarityLevel.EPIC, 100.0)
```

## 稀有度转换工具

### 静态方法

```gdscript
# 获取稀有度显示名称
var name = UpgradeOptionGenerator.get_rarity_display_name(UpgradeOptionGenerator.RarityLevel.RARE)
# 返回: "稀有"

# 获取稀有度英文键
var key = UpgradeOptionGenerator.get_rarity_key(UpgradeOptionGenerator.RarityLevel.EPIC)
# 返回: "epic"

# 从中文名称获取稀有度
var rarity = UpgradeOptionGenerator.get_rarity_from_display_name("传说")
# 返回: UpgradeOptionGenerator.RarityLevel.LEGENDARY

# 从英文键获取稀有度
var rarity = UpgradeOptionGenerator.get_rarity_from_key("common")
# 返回: UpgradeOptionGenerator.RarityLevel.COMMON

# 获取稀有度颜色
var color = UpgradeOptionGenerator.get_rarity_color(UpgradeOptionGenerator.RarityLevel.LEGENDARY)
# 返回: Color.PURPLE
```

## 统计和调试

### 获取统计信息

```gdscript
# 基本统计
var stats = generator.get_weight_config_stats()
print("总物品数量: ", stats.total_count)
print("总权重: ", stats.total_weight)

# 权重分布
var distribution = generator.get_rarity_weight_distribution()
for rarity in UpgradeOptionGenerator.RarityLevel.values():
    var data = distribution[rarity]
    print("稀有度 %s: %d 个物品, 平均权重: %.2f" % [
        UpgradeOptionGenerator.get_rarity_display_name(rarity),
        data.count,
        data.average_weight
    ])

# 打印完整统计信息
generator.print_weight_stats()
```

### 验证配置

```gdscript
# 验证所有配置的有效性
var invalid_ids = generator.validate_all_configs()
if invalid_ids.size() > 0:
    print("发现无效配置: ", invalid_ids)
```

## 与现有系统集成

### 弹球系统集成

```gdscript
# 从BallUIResource提取稀有度
var ball_resource = ball_instance.ui_resource
var rarity = UpgradeOptionGenerator.extract_ball_rarity(ball_resource)

# 批量添加弹球配置
var balls: Array[BallBase] = get_all_available_balls()
generator.add_balls_weight_configs(balls, 10.0)
```

### 遗物系统集成

```gdscript
# 从RelicUIData提取稀有度
var relic_ui_data = UIDataConverter.relic_to_ui_data(relic)
var rarity = UpgradeOptionGenerator.extract_relic_rarity(relic_ui_data)

# 批量添加遗物配置
var relics: Array[Relic] = get_all_available_relics()
generator.add_relics_weight_configs(relics, 8.0)
```

## 最佳实践

1. **权重设计原则**：
   - 普通物品权重较高（10-20）
   - 稀有物品权重中等（5-10）
   - 史诗物品权重较低（1-5）
   - 传说物品权重很低（0.1-1）

2. **批量调整建议**：
   - 游戏进度调整：随着玩家等级提升，可以提高高稀有度物品的权重
   - 平衡性调整：根据数据分析结果调整各稀有度的权重分布
   - 事件调整：特殊事件期间可以临时调整某些稀有度的权重

3. **性能考虑**：
   - 权重配置在游戏启动时初始化一次
   - 避免频繁的批量调整操作
   - 使用统计信息监控权重分布的合理性

## 注意事项

- 权重值必须 >= 0，系统会自动确保这一点
- 物品ID必须唯一，重复ID会覆盖之前的配置
- 稀有度转换支持中英文双向转换，确保与现有系统兼容
- 批量调整操作会影响所有匹配条件的物品，请谨慎使用
