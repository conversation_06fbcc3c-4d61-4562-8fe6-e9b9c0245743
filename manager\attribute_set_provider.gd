class_name AttributeSetProvider
extends RefCounted

## 统一的 AttributeSet 获取器
##
## 为不同系统提供统一的 AttributeSet 访问接口，
## 用于集中处理属性预览逻辑，避免代码重复

## 获取节点的 AttributeSet（统一接口调用）
##
## 使用开闭原则设计：各系统通过实现 get_attribute_set() 方法来提供自己的 AttributeSet
## 新增系统时无需修改此方法，只需实现统一接口即可
##
## @param node: 任意游戏对象节点
## @return: 该节点的 AttributeSet，如果没有则返回 null
static func get_attribute_set(node: Object) -> AttributeSet:
	if not is_instance_valid(node):
		return null

	# 统一接口调用：如果对象实现了 get_attribute_set() 方法，直接调用
	if node.has_method("get_attribute_set"):
		return node.get_attribute_set()

	return null


## 检查节点是否有属性系统
## @param node: 要检查的节点
## @return: 是否有可访问的属性系统
static func has_attribute_system(node: Object) -> bool:
	return get_attribute_set(node) != null


## 获取属性值（统一接口）
## @param node: 节点实例
## @param attribute_name: 属性名称
## @return: 属性值
static func get_attribute_value(node: Object, attribute_name: String) -> float:
	var attr_set = get_attribute_set(node)
	if not is_instance_valid(attr_set):
		return 0.0
	
	var attr = attr_set.find_attribute(attribute_name)
	return attr.get_value() if is_instance_valid(attr) else 0.0


## 获取指定等级下的属性值（统一接口）
## @param node: 节点实例
## @param attribute_name: 属性名称
## @param target_level: 目标等级
## @return: 指定等级下的属性值
static func get_attribute_value_at_level(node: Object, attribute_name: String, target_level: float) -> float:
	var attr_set = get_attribute_set(node)
	if not is_instance_valid(attr_set):
		return 0.0

	return _get_attribute_value_at_level_from_set(attr_set, attribute_name, target_level)


## 从 AttributeSet 获取指定等级下的属性值（内部方法）
## @param attr_set: AttributeSet 实例
## @param attribute_name: 属性名称
## @param target_level: 目标等级
## @return: 指定等级下的属性值
static func _get_attribute_value_at_level_from_set(attr_set: AttributeSet, attribute_name: String, target_level: float) -> float:
	# 检查属性是否存在
	if not attr_set.has_attribute(attribute_name):
		return 0.0

	# 特殊处理：如果请求的是等级属性本身
	if attribute_name == "level":
		return target_level

	# 检查是否有等级属性
	if not attr_set.has_attribute("level"):
		# 没有等级属性，返回预览值
		var attr = attr_set.find_attribute(attribute_name)
		return attr.get_preview_value() if is_instance_valid(attr) else 0.0

	var level_attr = attr_set.find_attribute("level")
	var target_attr = attr_set.find_attribute(attribute_name)

	if not is_instance_valid(level_attr) or not is_instance_valid(target_attr):
		return 0.0

	# 检查属性是否依赖等级
	if not _attribute_depends_on_level(target_attr):
		return target_attr.get_preview_value()

	# 保存当前等级
	var original_level: float = level_attr.get_value()

	# 如果目标等级与当前等级相同，直接返回预览值
	if is_equal_approx(target_level, original_level):
		return target_attr.get_preview_value()

	# 临时设置目标等级并计算属性值
	level_attr.set_value(target_level)
	var preview_value: float = target_attr.get_preview_value()

	# 恢复原等级
	level_attr.set_value(original_level)

	return preview_value


## 检查属性是否依赖于等级
## @param attribute: 要检查的属性
## @return: 如果属性依赖等级返回true，否则返回false
static func _attribute_depends_on_level(attribute: Attribute) -> bool:
	if not is_instance_valid(attribute):
		return false

	# 检查属性的依赖列表中是否包含"level"
	var dependencies: Array[String] = attribute.derived_from()
	return "level" in dependencies


## 获取指定等级偏移的属性值（统一接口）
## @param node: 节点实例
## @param attribute_name: 属性名称
## @param level_offset: 等级偏移量
## @return: 偏移等级下的属性值
static func get_attribute_value_with_offset(node: Object, attribute_name: String, level_offset: int) -> float:
	var current_level: float = get_attribute_value(node, "level")
	return get_attribute_value_at_level(node, attribute_name, current_level + level_offset)
