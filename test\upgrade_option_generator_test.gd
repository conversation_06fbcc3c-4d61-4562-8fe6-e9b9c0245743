extends Node

## 升级选项生成器测试脚本
##
## 用于测试升级选项生成器的稀有度系统功能
## 包括稀有度等级系统、权重配置和批量调整功能的测试

@onready var label: RichTextLabel = $ScrollContainer/RichTextLabel

func _ready() -> void:
	print("开始测试升级选项生成器...")
	label.append_text("[font_size=24][b]升级选项生成器测试[/b][/font_size]\n\n")
	
	# 测试稀有度系统
	test_rarity_system()
	
	# 测试权重配置系统
	test_weight_config_system()
	
	# 测试批量调整功能
	test_batch_adjustment()
	
	# 测试与现有系统的集成
	test_system_integration()
	
	print("升级选项生成器测试完成！")

## 测试稀有度系统
func test_rarity_system() -> void:
	print("\n=== 测试稀有度系统 ===")
	label.append_text("[font_size=18][b]稀有度系统测试[/b][/font_size]\n")
	
	# 测试稀有度名称转换
	var rarities = [
		UpgradeOptionGenerator.RarityLevel.COMMON,
		UpgradeOptionGenerator.RarityLevel.RARE,
		UpgradeOptionGenerator.RarityLevel.EPIC,
		UpgradeOptionGenerator.RarityLevel.LEGENDARY
	]
	
	for rarity in rarities:
		var display_name = UpgradeOptionGenerator.get_rarity_display_name(rarity)
		var key = UpgradeOptionGenerator.get_rarity_key(rarity)
		var color = UpgradeOptionGenerator.get_rarity_color(rarity)
		
		print("稀有度 %d: 显示名称=%s, 键=%s, 颜色=%s" % [rarity, display_name, key, str(color)])
		label.append_text("• %s (%s) - [color=%s]■[/color]\n" % [display_name, key, color.to_html()])
	
	# 测试反向转换
	print("\n测试反向转换:")
	var test_names = ["普通", "稀有", "史诗", "传说", "未知"]
	for test_name in test_names:
		var rarity = UpgradeOptionGenerator.get_rarity_from_display_name(test_name)
		print("从显示名称 '%s' 转换为稀有度: %d" % [test_name, rarity])

	var test_keys = ["common", "rare", "epic", "legendary", "unknown"]
	for test_key in test_keys:
		var rarity = UpgradeOptionGenerator.get_rarity_from_key(test_key)
		print("从键 '%s' 转换为稀有度: %d" % [test_key, rarity])
	
	label.append_text("\n")

## 测试权重配置系统
func test_weight_config_system() -> void:
	print("\n=== 测试权重配置系统 ===")
	label.append_text("[font_size=18][b]权重配置系统测试[/b][/font_size]\n")
	
	var generator = UpgradeOptionGenerator.new()
	
	# 添加测试配置
	generator.set_item_weight_config("fire_ball", UpgradeOptionGenerator.RarityLevel.COMMON, 10.0, "ball")
	generator.set_item_weight_config("ice_ball", UpgradeOptionGenerator.RarityLevel.RARE, 5.0, "ball")
	generator.set_item_weight_config("lightning_ball", UpgradeOptionGenerator.RarityLevel.EPIC, 2.0, "ball")
	generator.set_item_weight_config("dragon_ball", UpgradeOptionGenerator.RarityLevel.LEGENDARY, 0.5, "ball")
	
	generator.set_item_weight_config("iron_armor", UpgradeOptionGenerator.RarityLevel.COMMON, 8.0, "relic")
	generator.set_item_weight_config("magic_ring", UpgradeOptionGenerator.RarityLevel.RARE, 4.0, "relic")
	generator.set_item_weight_config("ancient_scroll", UpgradeOptionGenerator.RarityLevel.EPIC, 1.5, "relic")
	generator.set_item_weight_config("divine_artifact", UpgradeOptionGenerator.RarityLevel.LEGENDARY, 0.3, "relic")
	
	# 测试配置获取
	var config = generator.get_item_weight_config("fire_ball")
	if config:
		print("火球配置: 稀有度=%s, 权重=%.2f, 类型=%s" % [UpgradeOptionGenerator.get_rarity_display_name(config.rarity), config.weight, config.item_type])
		label.append_text("• 火球: %s, 权重=%.2f\n" % [UpgradeOptionGenerator.get_rarity_display_name(config.rarity), config.weight])
	
	# 测试按稀有度筛选
	var common_configs = generator.get_configs_by_rarity(UpgradeOptionGenerator.RarityLevel.COMMON)
	print("普通稀有度物品数量: %d" % common_configs.size())
	label.append_text("• 普通稀有度物品: %d 个\n" % common_configs.size())
	
	# 测试按类型筛选
	var ball_configs = generator.get_configs_by_type("ball")
	print("弹球类型物品数量: %d" % ball_configs.size())
	label.append_text("• 弹球类型物品: %d 个\n" % ball_configs.size())
	
	# 打印统计信息
	generator.print_weight_stats()
	
	label.append_text("\n")

## 测试批量调整功能
func test_batch_adjustment() -> void:
	print("\n=== 测试批量调整功能 ===")
	label.append_text("[font_size=18][b]批量调整功能测试[/b][/font_size]\n")
	
	var generator = UpgradeOptionGenerator.new()
	
	# 添加测试数据
	generator.set_item_weight_config("common_item1", UpgradeOptionGenerator.RarityLevel.COMMON, 10.0, "ball")
	generator.set_item_weight_config("common_item2", UpgradeOptionGenerator.RarityLevel.COMMON, 15.0, "ball")
	generator.set_item_weight_config("rare_item1", UpgradeOptionGenerator.RarityLevel.RARE, 5.0, "relic")
	generator.set_item_weight_config("rare_item2", UpgradeOptionGenerator.RarityLevel.RARE, 8.0, "relic")
	
	print("调整前的权重分布:")
	var distribution_before = generator.get_rarity_weight_distribution()
	for rarity in UpgradeOptionGenerator.RarityLevel.values():
		var rarity_name = UpgradeOptionGenerator.get_rarity_display_name(rarity)
		var data = distribution_before[rarity]
		if data.count > 0:
			print("  %s: %d 个物品, 平均权重: %.2f" % [rarity_name, data.count, data.average_weight])
			label.append_text("• 调整前 %s: %d 个, 平均权重: %.2f\n" % [rarity_name, data.count, data.average_weight])
	
	# 测试加法调整
	var affected = generator.adjust_rarity_weights_add(UpgradeOptionGenerator.RarityLevel.COMMON, 5.0)
	print("普通稀有度权重增加5.0，影响物品数: %d" % affected)
	
	# 测试乘法调整
	affected = generator.adjust_rarity_weights_multiply(UpgradeOptionGenerator.RarityLevel.RARE, 1.5)
	print("稀有稀有度权重乘以1.5，影响物品数: %d" % affected)
	
	print("调整后的权重分布:")
	var distribution_after = generator.get_rarity_weight_distribution()
	for rarity in UpgradeOptionGenerator.RarityLevel.values():
		var rarity_name = UpgradeOptionGenerator.get_rarity_display_name(rarity)
		var data = distribution_after[rarity]
		if data.count > 0:
			print("  %s: %d 个物品, 平均权重: %.2f" % [rarity_name, data.count, data.average_weight])
			label.append_text("• 调整后 %s: %d 个, 平均权重: %.2f\n" % [rarity_name, data.count, data.average_weight])
	
	# 测试设置固定权重
	affected = generator.set_rarity_weights(UpgradeOptionGenerator.RarityLevel.COMMON, 20.0)
	print("设置普通稀有度权重为20.0，影响物品数: %d" % affected)
	
	label.append_text("\n")

## 测试与现有系统的集成
func test_system_integration() -> void:
	print("\n=== 测试系统集成 ===")
	label.append_text("[font_size=18][b]系统集成测试[/b][/font_size]\n")
	
	var generator = UpgradeOptionGenerator.new()
	
	# 测试从弹球资源提取稀有度
	var ball_resource = BallUIResource.new()
	ball_resource.ball_name = "测试弹球"
	ball_resource.rarity = "史诗"
	
	var extracted_rarity = UpgradeOptionGenerator.extract_ball_rarity(ball_resource)
	print("从弹球资源提取稀有度: %s -> %d" % [ball_resource.rarity, extracted_rarity])
	label.append_text("• 弹球稀有度提取: %s -> %s\n" % [ball_resource.rarity, UpgradeOptionGenerator.get_rarity_display_name(extracted_rarity)])
	
	# 测试从遗物UI数据提取稀有度
	var relic_ui_data = RelicUIData.new("测试遗物", "测试描述", null, "epic")
	extracted_rarity = UpgradeOptionGenerator.extract_relic_rarity(relic_ui_data)
	print("从遗物UI数据提取稀有度: %s -> %d" % [relic_ui_data.rarity, extracted_rarity])
	label.append_text("• 遗物稀有度提取: %s -> %s\n" % [relic_ui_data.rarity, UpgradeOptionGenerator.get_rarity_display_name(extracted_rarity)])
	
	# 测试实际弹球实例（如果可用）
	test_real_ball_integration(generator)
	
	# 测试实际遗物实例（如果可用）
	test_real_relic_integration(generator)
	
	label.append_text("\n")

## 测试真实弹球实例集成
func test_real_ball_integration(generator: UpgradeOptionGenerator) -> void:
	# 尝试加载一个真实的弹球预制体进行测试
	var ball_scene = preload("res://ball/prefab/poison_ball.tscn")
	if ball_scene:
		var ball_instance = ball_scene.instantiate()
		add_child(ball_instance)
		
		if ball_instance is BallBase and ball_instance.ui_resource:
			generator.add_ball_weight_config(ball_instance, 10.0)
			print("成功添加真实弹球权重配置: %s" % ball_instance.ui_resource.ball_name)
			label.append_text("• 真实弹球集成: %s\n" % ball_instance.ui_resource.ball_name)
		
		ball_instance.queue_free()

## 测试真实遗物实例集成
func test_real_relic_integration(generator: UpgradeOptionGenerator) -> void:
	# 尝试加载一个真实的遗物资源进行测试
	var relic_resource = preload("res://relic/relics/iron_armor.tres")
	if relic_resource and relic_resource is Relic:
		generator.add_relic_weight_config(relic_resource, 8.0)
		print("成功添加真实遗物权重配置: %s" % relic_resource.name)
		label.append_text("• 真实遗物集成: %s\n" % relic_resource.name)
