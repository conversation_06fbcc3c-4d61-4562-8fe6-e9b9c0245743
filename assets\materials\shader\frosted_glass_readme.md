# 毛玻璃效果 Shader 使用说明（扭曲 + 高斯模糊）

## 概述

这个 shader 基于 Shadertoy 代码转换为 Godot 4.4 兼容格式，提供了两种毛玻璃效果：
- **模式 0**：静态毛玻璃扭曲效果
- **模式 1**：高斯模糊效果

适用于 ColorRect 节点，可以对整个覆盖区域产生不同类型的毛玻璃效果。

## 文件说明

- `frosted_glass.gdshader` - 主要的 shader 文件
- `frosted_glass_noise.tres` - 噪声纹理资源（可选）
- `test_shader.tscn` - 测试场景
- `frosted_glass_readme.md` - 本说明文档

## 使用方法

### 1. 基本设置

1. 创建一个 ColorRect 节点
2. 在 Material 属性中创建新的 ShaderMaterial
3. 将 `frosted_glass.gdshader` 设置为 shader
4. 调整 ColorRect 的大小和位置来覆盖需要毛玻璃效果的区域

### 2. 参数说明

#### 效果模式选择
- **effect_mode** (0-1): 效果模式选择
  - 0 = 毛玻璃扭曲效果
  - 1 = 高斯模糊效果

#### 通用参数
- **alpha** (0.0-1.0): 整体透明度，控制毛玻璃效果的透明程度

#### 扭曲模式参数（仅在 effect_mode = 0 时使用）
- **distortion_strength** (0.0-2.0): 扭曲强度，控制毛玻璃扭曲效果的强度
- **noise_distortion** (0.0-0.2): 噪声扭曲强度，控制噪声纹理对扭曲的影响程度
- **noise_scale** (0.1-10.0): 噪声缩放，控制噪声纹理的缩放程度，影响扭曲的细节密度
- **use_noise_texture** (bool): 是否使用噪声纹理，如果为 false 将使用程序化噪声
- **noise_texture**: 噪声纹理，用于产生扭曲效果（可选）

#### 高斯模糊参数（仅在 effect_mode = 1 时使用）
- **blur_radius** (0.0-10.0): 模糊半径，控制高斯模糊的范围，值越大模糊效果越强
- **blur_quality** (0-2): 模糊质量，控制模糊采样的质量和性能平衡
  - 0 = 低质量（9次采样 + LOD0，性能最佳）
  - 1 = 中等质量（25次采样 + LOD1，推荐）
  - 2 = 高质量（35次采样 + LOD2，效果最佳，16x加速优化）

## 效果调节建议

### 扭曲模式效果预设

#### 轻微扭曲效果
```
effect_mode = 0
alpha = 0.9
distortion_strength = 0.5
noise_distortion = 0.02
noise_scale = 1.0
```

#### 中等扭曲效果（推荐）
```
effect_mode = 0
alpha = 0.8
distortion_strength = 1.0
noise_distortion = 0.05
noise_scale = 2.0
```

#### 强烈扭曲效果
```
effect_mode = 0
alpha = 0.7
distortion_strength = 1.5
noise_distortion = 0.1
noise_scale = 3.0
```

### 高斯模糊模式效果预设

#### 轻微模糊效果
```
effect_mode = 1
alpha = 0.9
blur_radius = 1.0
blur_quality = 0
```

#### 中等模糊效果（推荐）
```
effect_mode = 1
alpha = 0.8
blur_radius = 2.0
blur_quality = 1
```

#### 强烈模糊效果
```
effect_mode = 1
alpha = 0.7
blur_radius = 4.0
blur_quality = 2
```

## 性能优化

### 扭曲模式优化
1. **降低扭曲强度**: 减少 `distortion_strength` 和 `noise_distortion` 值
2. **使用程序化噪声**: 设置 `use_noise_texture = false` 可以减少纹理采样
3. **调整噪声缩放**: 较小的 `noise_scale` 值可以减少计算复杂度

### 高斯模糊模式优化
1. **降低模糊质量**: 使用 `blur_quality = 0` 可以显著提升性能
2. **减少模糊半径**: 较小的 `blur_radius` 值可以减少采样次数
3. **MIPmap 加速**: 高质量模式使用 MIPmap 技术实现 16x 加速优化
4. **性能对比**:
   - 质量 0：9次采样 + LOD0，性能最佳
   - 质量 1：25次采样 + LOD1，平衡选择
   - 质量 2：35次采样 + LOD2，效果最佳，已优化性能

### 模式选择建议
- **移动设备**: 推荐使用扭曲模式或低质量模糊模式
- **桌面设备**: 可以使用高质量模糊模式获得更好效果

## 注意事项

1. 确保 ColorRect 节点在需要毛玻璃效果的内容之上
2. 如果使用噪声纹理，建议使用无缝纹理以避免边缘问题
3. 在移动设备上建议降低扭曲强度以保证性能
4. 静态效果覆盖整个 ColorRect 区域，无需担心区域限制
5. 可以通过脚本动态调整参数来实现不同的静态效果

## 故障排除

### 效果不可见
- 检查 alpha 值是否过高（接近 1.0）
- 确认 effect_mode 设置正确
- 验证 ColorRect 的位置和大小设置
- 扭曲模式：确认 distortion_strength 不为 0
- 模糊模式：确认 blur_radius 不为 0

### 性能问题
- 扭曲模式：降低 distortion_strength 和 noise_distortion，使用程序化噪声
- 模糊模式：降低 blur_quality 和 blur_radius
- 减少 ColorRect 的覆盖面积
- 在移动设备上优先使用扭曲模式

### 扭曲效果过强
- 降低 noise_distortion 值
- 减少 distortion_strength 值
- 增加 noise_scale 值以获得更细腻的扭曲

### 模糊效果不理想
- 调整 blur_radius 以获得合适的模糊范围
- 提高 blur_quality 以获得更平滑的模糊效果
- 注意高质量模糊会显著影响性能

## 技术特性

### 高斯模糊优化技术
- **MIPmap 加速**: 使用 `textureLod()` 在不同 MIP 级别采样，实现 16x 性能提升
- **自适应采样**: 根据质量级别自动调整采样数量和 LOD 级别
- **高斯权重分布**: 使用标准高斯分布公式确保模糊效果的数学正确性
- **内存优化**: 通过 MIPmap 减少纹理带宽消耗

### 扭曲效果特性
- **静态噪声**: 基于位置的静态噪声，无时间依赖
- **全区域覆盖**: 扭曲效果均匀分布在整个区域
- **可配置噪声**: 支持纹理噪声和程序化噪声

## 扩展功能

可以通过修改 shader 代码添加以下功能：
- 颜色调整和色调映射
- 更复杂的静态扭曲模式
- 基于位置的渐变扭曲
- 多层噪声混合
- 动态模糊半径
- 方向性模糊效果
