@tool
class_name LeveledAttribute
extends Attribute


## 每级增长值
@export var growth_per_level: float = 0.0

const LEVEL_GROWTH_BUFF_NAME = "level_growth_buff"

func custom_compute(operated_value: float, _compute_params: Array[Attribute]) -> float:
	var level_attribute = _compute_params[0]
	return base_value + (level_attribute.get_value() - 1) * growth_per_level


## 属性依赖列表
## @ return: 返回依赖属性的名称数组
func derived_from() -> Array[String]:
	return [
		"level"
	]
