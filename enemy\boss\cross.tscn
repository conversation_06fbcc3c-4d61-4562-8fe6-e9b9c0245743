[gd_scene load_steps=5 format=3 uid="uid://p1rnx2obikak"]

[ext_resource type="PhysicsMaterial" uid="uid://btxphd0oijm3l" path="res://assets/materials/physics/enemy.tres" id="1_ucrri"]
[ext_resource type="Texture2D" uid="uid://u5ndsxu34wr3" path="res://assets/imgs/balls/128x128.png" id="2_00dtj"]
[ext_resource type="Script" uid="uid://bd1ch0opqm2dh" path="res://enemy/boss/cross.gd" id="2_uvm5e"]

[sub_resource type="RectangleShape2D" id="RectangleShape2D_uvm5e"]
size = Vector2(25.5, 256)

[node name="cross" type="RigidBody2D"]
position = Vector2(405, 292)
collision_layer = 4
collision_mask = 6
physics_material_override = ExtResource("1_ucrri")
gravity_scale = 0.0
freeze = true
freeze_mode = 1
linear_damp_mode = 1
angular_damp_mode = 1
script = ExtResource("2_uvm5e")

[node name="Sprite2D" type="Sprite2D" parent="."]
scale = Vector2(0.2, 2)
texture = ExtResource("2_00dtj")

[node name="CollisionShape2D" type="CollisionShape2D" parent="."]
shape = SubResource("RectangleShape2D_uvm5e")
