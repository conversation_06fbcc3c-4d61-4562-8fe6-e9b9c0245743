shader_type canvas_item;

// --- Uniforms --- //
instance uniform float percentage: hint_range(0.0, 1.0, 0.01) = 1.0;
uniform float debug_percentage: hint_range(0.0, 1.0, 0.01) = 1.0;

uniform sampler2D burn_texture;
group_uniforms layer_1;
uniform vec4 layer_1: source_color = vec4(0.2, 0.2, 0.2, 1.0);
uniform float size_1 = 0.05;
group_uniforms layer_2;
uniform vec4 layer_2: source_color = vec4(1.0, 0.0, 0.0, 1.0);
uniform float size_2 = 0.05;
group_uniforms layer_3;
uniform vec4 layer_3: source_color = vec4(1.0, 0.5, 0.0, 1.0);
uniform float size_3 = 0.05;
uniform float alpha_threshold: hint_range(0.0, 1.0, 0.01) = 0.0;

void fragment() {
	vec4 tex_color = texture(TEXTURE, UV);
	float noise = texture(burn_texture, UV).r * (1.0 - (size_1 + size_2 + size_3 + 0.01));

	// Calculate alpha based on the dissolve effect
	float dissolve_percentage = percentage * debug_percentage;
	float dissolve_alpha = step(noise, dissolve_percentage);

	// Only apply the effect to non-transparent parts of the original texture
	if (tex_color.a <= alpha_threshold) {
		COLOR = vec4(0.0, 0.0, 0.0, 0.0);
		discard;
	} else {
		// Apply dissolve effect
		COLOR.a = tex_color.a * dissolve_alpha;

		// Only apply the burning effect where the dissolve is happening
		if (dissolve_alpha > 0.0) {
			// Apply color layers only near the dissolve edge
			if (noise > dissolve_percentage - (size_1 + size_2 + size_3) && noise < dissolve_percentage) {
				if (noise > dissolve_percentage - size_1) {
					COLOR.rgb = mix(tex_color.rgb, layer_1.rgb, layer_1.a);
				} else if (noise > dissolve_percentage - (size_1 + size_2)) {
					COLOR.rgb = mix(tex_color.rgb, layer_2.rgb, layer_2.a);
				} else {
					COLOR.rgb = mix(tex_color.rgb, layer_3.rgb, layer_3.a);
				}
			} else {
				COLOR.rgb = tex_color.rgb;
			}
		}
	}
}
