[gd_resource type="ShaderMaterial" load_steps=8 format=3 uid="uid://u3rtsamehui"]

[ext_resource type="Shader" uid="uid://c1qpfaitcf7lw" path="res://assets/materials/shader/noise_cover.gdshader" id="1_ii0yo"]

[sub_resource type="FastNoiseLite" id="FastNoiseLite_l0yvr"]
noise_type = 4
frequency = 0.0371
fractal_type = 2
fractal_lacunarity = 1.655
fractal_gain = 0.685

[sub_resource type="NoiseTexture2D" id="NoiseTexture2D_fvqrq"]
seamless = true
noise = SubResource("FastNoiseLite_l0yvr")

[sub_resource type="FastNoiseLite" id="FastNoiseLite_73ceo"]
noise_type = 0
frequency = 0.0042
fractal_type = 2

[sub_resource type="NoiseTexture2D" id="NoiseTexture2D_wqtjp"]
seamless = true
noise = SubResource("FastNoiseLite_73ceo")

[sub_resource type="Gradient" id="Gradient_b2d00"]
colors = PackedColorArray(0.5, 0.7, 1.3, 1, 0, 1.3, 1.3, 1)

[sub_resource type="GradientTexture1D" id="GradientTexture1D_xts7u"]
gradient = SubResource("Gradient_b2d00")
use_hdr = true

[resource]
shader = ExtResource("1_ii0yo")
shader_parameter/noise_pattern = SubResource("NoiseTexture2D_fvqrq")
shader_parameter/noise_pattern2 = SubResource("NoiseTexture2D_wqtjp")
shader_parameter/scroll = Vector2(0.05, 0.08)
shader_parameter/scrol2 = Vector2(0.02, 0.05)
shader_parameter/overlap_color_gradient = SubResource("GradientTexture1D_xts7u")
shader_parameter/color_intensity = 2.1
shader_parameter/threshold = 0.314
shader_parameter/noise_alpha = 1.0
