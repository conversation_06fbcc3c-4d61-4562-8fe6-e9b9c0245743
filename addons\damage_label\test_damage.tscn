[gd_scene load_steps=6 format=3 uid="uid://xhnshew7ev16"]

[ext_resource type="Script" uid="uid://dd1tuqanvxf4j" path="res://addons/damage_label/test_damage.gd" id="1_smt80"]
[ext_resource type="Script" uid="uid://brw8mhd57li2x" path="res://addons/damage_label/damage_label_manager.gd" id="2_pt33o"]
[ext_resource type="PackedScene" uid="uid://hgp2bqgeme43" path="res://addons/damage_label/damage_label.tscn" id="3_ravhu"]

[sub_resource type="Curve" id="Curve_7wtm5"]
_data = [Vector2(0, 0), 0.0, 0.0, 0, 0, Vector2(0.0805471, 1), 0.0, 0.0, 0, 0, Vector2(0.141337, 0.585319), 0.0, 0.0, 0, 0, Vector2(1, 0.377146), 0.0, 0.0, 0, 0]
point_count = 4

[sub_resource type="Curve" id="Curve_5dux7"]
_data = [Vector2(0, 1), 0.0, 0.0, 0, 0, Vector2(0.49848, 1), 0.0, 0.0, 0, 0, Vector2(1, 0.420446), 0.0, 0.0, 0, 0]
point_count = 3

[node name="TestDamage" type="Node2D"]
script = ExtResource("1_smt80")

[node name="DamageManager" type="Node" parent="."]
script = ExtResource("2_pt33o")
label_scene = ExtResource("3_ravhu")
pool_size = 500
global_acceleration = Vector2(0, 0)
initial_velocity_min = Vector2(0, -100)
initial_velocity_max = Vector2(0, -100)
scale_curve = SubResource("Curve_7wtm5")
alpha_curve = SubResource("Curve_5dux7")
metadata/_custom_type_script = "uid://brw8mhd57li2x"
