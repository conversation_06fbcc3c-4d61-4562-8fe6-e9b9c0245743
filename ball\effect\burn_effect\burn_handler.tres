[gd_resource type="Resource" script_class="BurnEffectHandler" load_steps=4 format=3 uid="uid://dl2jolg224tif"]

[ext_resource type="Material" uid="uid://c54kcr0axfmtf" path="res://ball/effect/burn_effect/burn_debuff_material.tres" id="1_6ceg4"]
[ext_resource type="Script" uid="uid://b5lii3xn3bsgk" path="res://enemy/buff_handlers/burn_effect_handler.gd" id="2_54uhb"]
[ext_resource type="Material" uid="uid://x372txrlt4xk" path="res://ball/effect/burn_effect/burn_to_die_material.tres" id="2_ivica"]

[resource]
resource_local_to_scene = true
script = ExtResource("2_54uhb")
burn_material = ExtResource("1_6ceg4")
burn_to_die_material = ExtResource("2_ivica")
damage_color = Color(1, 0.564706, 0, 1)
handled_buff_name = "burn"
metadata/_custom_type_script = "uid://b5lii3xn3bsgk"
